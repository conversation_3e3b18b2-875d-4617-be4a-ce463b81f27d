# Unified Portfolio Service

## Purpose

Here's the original proposal for this service:
https://icapitalnetwork.atlassian.net/wiki/spaces/ENG/pages/**********/UPS+Technical+Design

To very briefly summarize the motivating problem:

"Currently at iCapital, we have several disjointed representations of client portfolios and their respective holdings. These are mostly segmented across asset classes (Alts, SI, Annuities). In the case of SI, we also have disjointed representations of pre-trade (order) accounts vs post-trade (holdings) accounts.  This setup leads to various limitations and inefficiencies when building features that require cross-asset and cross-lifecycle considerations."

Unified Portfolio Service aims to comprehensively represent all Holdings and Ownership structures, across asset classes.

Note: some details have evolved and changed since the original proposal.

## Platform

This project leverages the "Simon architecture".  This involves many things.  Some of the most important:

* Scala2 is the primary programming language.
* MongoDB is the primary database store.
* Simon Entitlements System: https://icapitalnetwork.atlassian.net/wiki/spaces/CORE/pages/**********/SIMON+Entitlements+Library
* Extensive use of Simon libraries for the original application template and many other things; all kickstarted by `simon create`


## Core entities

### Holdings

All types of Holdings (Structured Investments, Annuities, Traditional Investments, Alternative Investments, and more) should have a home here.  Each such Holding will have a Document in the Holdings collection.

### Owners

An Owner is anything that can, directly or indirectly, possess Holdings.  Some examples of such things:

* Accounts
* Legal Entities
    * People
    * Trusts
    * Companies
* Households

We have made a common abstraction that encompasses all of these things, called Owner.  All such Owners will have a Document in the Owners collection.

We have a permissive structure for Ownership heirarchy.  While the most common case is for Households to contain LegalEntities, LegalEntities to contain Accounts, and Accounts to directly contain Holdings, our ownership model is flexible enough to account for any type of Owner to contain any other type of Owner, in any combination, any cardinality, and at any level of depth.  Some scenerios that this will help to model successfully:

* Shared Trust (LegalEntity) belonging to multiple Households.
* Multiple Households belonging to a larger extended family (another Household). 
* Parent (LegalEntity) having dependent children (other LegalEntities) with their own bank accounts (Accounts). 
* Anything unexpected that we might want to import from another permissive system, such as Addepar.


## Root modules

### com.simonmarkets.unifiedportfolio

This is the "Core Module" for the service.  Its data models describe EVERYTHING that can be stored in the UPS database.  Its API's are general purpose API's for clients that may be interested in "everything UPS knows" about Owners and/or Holdings.

### com.simonmarkets.regbi

This is a "Presentation Layer" specifically for the RegBI feature (initially sponsored by LPL).  It still leverages the same Mongo collections as the Core Module, but it presents limited slices of the data, in a way that is tailored to satisfy very specific UI pages within the RegBI product.


## Philosophy

### Nested documents and sealed traits

In general, when we have multiple things that have some similarities but also some differences (for example, SI Accounts versus Households), the commonalities will be defined in a Case Class (in this case, Owner).  The differences will be isolated to a specific field of the parent Case Class , whose type will be a Sealed Trait 
(in this case, OwnerDetails).  For each subtype of the parent entity (in this case, LegalEntity, Account, Household), a separate case class will extend the Sealed Trait field (in this case, e.g. AccountDetails extending OwnerDetails).  

This lets us have the following nice qualities:

* You can look at one thing (e.g. Owner) to understand what is common across all variants of a concept.
* You can look at one other thing (e.g. AccountDetails) to understand what is specific to a specific variant.
* The model makes it impossible for a concept to have more than one variant in a particular aspect simultaneously 
* You don't have attributes of the abstraction (Owner) that are optional ONLY BECAUSE e.g. not all Owners are Accounts.  In other words, an attribute of AccountDetails can be required - but it will only be required for Owners that are actually Accounts because only THOSE Owners will have an AccountDetails.
* It lends itself well to eventual representation in a relational database (which we'll certainly need to do via Ubertable).




