proxySystemProperty: "-Dhttps.proxyHost=internal-alpha-squid-proxy-elb-**********.us-east-1.elb.amazonaws.com -Dhttps.proxyPort=3128"

provider:
  profile: "alpha"
  securityGroupIds:
    - "sg-04a9f78088f2c60b3"
  subnetIds:
    - "subnet-092aa15246e226be2"
    - "subnet-0978a24297da4c96e"
    - "subnet-0c85b6c43be91a809"
  accountId: ************
  iam:
    role: arn:aws:iam::************:role/service-rainbow-portfolio-upload-service-alpha-role
