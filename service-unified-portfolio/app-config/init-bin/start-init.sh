#!/bin/bash

echo "andy here is the ls"
ls /opt/simon/

cp /opt/simon/etc/supervisord.conf /etc/.

mkdir -p /var/cv/creds
if [[ $config_env == "prod" ]]
then
   aws s3 cp s3://prod-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
elif [[ $config_env == "qa" ]]
then
   aws s3 cp s3://qa-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
else
   aws s3 cp s3://alpha-simon-config-data/creds/app-common/simon-vertx-ssl.jks /var/cv/creds/simon-vertx-ssl.jks
fi
