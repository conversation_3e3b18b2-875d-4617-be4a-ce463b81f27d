<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <version>sandbox-SNAPSHOT</version>

    <parent>
        <artifactId>unified-portfolio</artifactId>
        <groupId>com.simonmarkets.unified-portfolio</groupId>
        <version>sandbox-SNAPSHOT</version>
    </parent>

    <artifactId>service-unified-portfolio</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.circe</groupId>
            <artifactId>circe-generic-extras_${scala.version.short}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.snowflake</groupId>
            <artifactId>snowpark</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.resteasy</groupId>
            <artifactId>lib-resteasy-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-logging-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-acl-client-resteasy</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.goldmansachs.marquee</groupId>
            <artifactId>lib-encryption-simon</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>casbah_${scala.version.short}</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-useracl-directive-resteasy</artifactId>
        </dependency>
        <dependency>
            <groupId>io.simon</groupId>
            <artifactId>lib-openapi-generator</artifactId>
        </dependency>

        <!-- Mongo dependency -->
        <dependency>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>lib-data-mongo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.resteasy</groupId>
            <artifactId>lib-resteasy-mongodb</artifactId>
        </dependency>

        <!-- Required despite the scalatest-maven-plugin plugin dependency below; the "aws" profile build seems to fail without it -->
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${scala.version.short}</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.simonmarkets.utils</groupId>
            <artifactId>scala-mongo-embedded-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>assembly.xml</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <configuration>
                    <parallel>false</parallel>
                </configuration>
            </plugin>
            <plugin>
                <groupId>de.smartics.maven.plugin</groupId>
                <artifactId>buildmetadata-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.github.git-commit-id</groupId>
                <artifactId>git-commit-id-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>service-unified-portfolio</id>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <finalName>service-unified-portfolio</finalName>
                            <descriptors>
                                <descriptor>src/main/resources/assembly.xml</descriptor>
                            </descriptors>
                            <tarLongFileMode>gnu</tarLongFileMode>
                        </configuration>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
