package com.simonmarkets.regbi

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.logging.TraceId
import com.simonmarkets.unifiedportfolio.domain.data.{AccountDetails, Authorization, ExternalId, Holding, HouseholdDetails, InvestorExperience, LPLAccountExtensions, LegalEntityDetails, Money, Owner, Person, ProductId, ProductType, RecordTimestamps, StructuredInvestmentDetails}
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.{LPLAccountPrimaryClassification, LPLAccountRType, LPLAccountSecondaryClassification}
import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, ExperienceProductType, HoldingType, LegalEntityType, OwnerType}
import io.simon.encryption.v2.model.EncryptedData
import simon.Id.NetworkId

import java.time.LocalDateTime


trait TestData {
  private val networkId = NetworkId("LPL")

  implicit val traceId: TraceId = TraceId.randomize
  implicit val userACL: UserACL = UserACL(
    userId = "testUser",
    networkId = networkId,
    lastVisitedAt = Some(LocalDateTime.now),
    email = "<EMAIL>",
    firstName = "firstName",
    lastName = "lastName",
    distributorId = None,
    omsId = None,
    tradewebEligible = false,
    regSEligible = false,
    isActive = Some(true),
    capabilities = Set("viewUnifiedOwnerViaNetwork", "editUnifiedOwnerViaNetwork", "viewUnifiedHoldingViaNetwork")
  )

  val viewAccessKeys: Set[String] = Set("viewUnifiedOwnerViaNetwork:LPL", "viewUnifiedHoldingViaNetwork:LPL")

  val defaultHouseholdDetails: HouseholdDetails = HouseholdDetails(
    liquidNetWorthMin = Some(Money(Currency.USD, 100000)),
    liquidNetWorthMax = Some(Money(Currency.USD, 100000)),
    annualExpenses = Some(Money(Currency.USD, 1000)),
    availableLCF = Some(Money(Currency.USD, 1000)),
    obligations = Some(Money(Currency.USD, 1000)),
    liquidNetWorth = Some(Money(Currency.USD, 100000))
  )

  val defaultAccountDetails: AccountDetails = AccountDetails(
    accountType = Some("SAM"),
    accountNumber = None,
    investmentObjective = Some("growth"),
    totalValue = Some(Money(Currency.USD, 1000)),
    documents = Set.empty,
    partnerExtensionsLPL = Some(LPLAccountExtensions(primaryClassification = LPLAccountPrimaryClassification.SAM, secondaryClassification = LPLAccountSecondaryClassification.A1H, rType = LPLAccountRType.FS)),
    None,
    None
  )

  val defaultPersonEntityDetails: Person = Person(
    experience = Some(InvestorExperience(investmentType = "SI", years = 5)),
    age = Some(29),
    productTypes = Set(ProductType(ExperienceProductType.`Structured/Market-Linked Growth Notes`, 3))
  )

  val defaultLegalEntityDetails: LegalEntityDetails = LegalEntityDetails(
    taxId = None,
    entityType = LegalEntityType.Person,
    entity = defaultPersonEntityDetails
  )

  val defaultHousehold1: Owner = Owner(
    id = "householdId1",
    acceptedAccessKeys = viewAccessKeys,
    ownerType = OwnerType.Household,
    name = EncryptedData("some name here", "some name here", Array[scala.Byte](), None),
    externalIds = Set.empty,
    parentIds = Set.empty,
    childIds = Set.empty,
    auth = Authorization(Set.empty, Set.empty, Set.empty, Set.empty, NetworkId("LPL")),
    timestamps = RecordTimestamps(LocalDateTime.now(), LocalDateTime.now()),
    details = defaultHouseholdDetails
  )
  val defaultHousehold2: Owner = Owner(
    id = "householdId2",
    acceptedAccessKeys = viewAccessKeys,
    ownerType = OwnerType.Household,
    name = EncryptedData("some name here", "some name here", Array[scala.Byte](), None),
    externalIds = Set.empty,
    parentIds = Set.empty,
    childIds = Set.empty,
    auth = Authorization(Set.empty, Set.empty, Set.empty, Set.empty, NetworkId("LPL")),
    timestamps = RecordTimestamps(LocalDateTime.now(), LocalDateTime.now()),
    details = defaultHouseholdDetails.copy(liquidNetWorth = None)
  )
  val defaultHousehold3: Owner = Owner(
    id = "householdId3",
    acceptedAccessKeys = Set("viewUnifiedOwnerViaNetwork:SomeOtherLPL"),
    ownerType = OwnerType.Household,
    name = EncryptedData("some name here", "some name here", Array[scala.Byte](), None),
    externalIds = Set.empty,
    parentIds = Set.empty,
    childIds = Set.empty,
    auth = Authorization(Set.empty, Set.empty, Set.empty, Set.empty, NetworkId("LPL")),
    timestamps = RecordTimestamps(LocalDateTime.now(), LocalDateTime.now()),
    details = defaultHouseholdDetails
  )

  val defaultLegalEntity: Owner = defaultHousehold1.copy(
    ownerType = OwnerType.LegalEntity,
    parentIds = Set(defaultHousehold1.id),
    id = "legalEntity1",
    details = defaultLegalEntityDetails
  )

  val defaultAccount: Owner = defaultHousehold1.copy(
    ownerType = OwnerType.Account,
    parentIds = Set(defaultLegalEntity.id),
    id = "accountId1",
    details = defaultAccountDetails
  )

  val defaultHolding: Holding = Holding(
    id = "someHolding1",
    acceptedAccessKeys = viewAccessKeys,
    holdingType = HoldingType.StructuredInvestment,
    productId = ProductId(
      Some("1"),
      Some("1"),
      Some("1"),
      Set(ExternalId("1", "lpl", "LPL", false)),
    ),
    marketValue = Some(Money(amount = 20000, currency = Currency.USD)),
    totalCommitment = Some(Money(amount = 10000, currency = Currency.USD)),
    withdrawableAmount = Some(Money(amount = 10000, currency = Currency.USD)),
    ownerships = Set.empty,
    ownerAncestorIds = Set(defaultHousehold1.id),
    auth = Authorization(Set.empty, Set.empty, Set.empty, Set.empty, NetworkId("LPL")),
    timestamps = RecordTimestamps(LocalDateTime.now(), LocalDateTime.now()),
    details = StructuredInvestmentDetails()
  )
}
