package com.simonmarkets.regbi.repository

import com.simonmarkets.regbi.TestData
import com.simonmarkets.regbi.domain.{HouseholdSortBy, SortOrder}
import com.simonmarkets.unifiedportfolio.domain.data.{Holding, Money, Owner}
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}
import com.simonmarkets.utils.data.mongo.ops.RegularMongoGenericRepositoryOps._
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.unifiedportfolio.domain.enums.Currency
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository.HoldingRepositoryMongo

import scala.concurrent.ExecutionContext.Implicits.global


class OwnersPresentationRepositoryMongoSpec extends WordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfter with TestData {
  implicit lazy val dbCtx: DatabaseContext = DatabaseContext(db, client)
  private lazy val ownersRepo = new OwnersPresentationRepositoryMongo()
  private lazy val ownersCollection: MongoCollection[Owner] = ownersRepo.mongoOps.getCollection

  private lazy val holdingsRepo = new HoldingRepositoryMongo()
  private lazy val holdingsCollection: MongoCollection[Holding] = holdingsRepo.mongoOps.getCollection

  before {
    ownersCollection.drop().toFuture.await
    ownersCollection.insertMany(Seq(defaultHousehold1, defaultHousehold2, defaultHousehold3)).toFuture.await
    holdingsCollection.drop().toFuture.await
  }

  "getHouseholdNames" should {
    "return the names and ids of all households by accepted access keys" in {
      val res = ownersRepo.getHouseholdNames(Set("viewUnifiedOwnerViaNetwork:LPL")).await
      res.size shouldBe 2
      val ids = res.map(_.id)
      ids should contain(defaultHousehold1.id)
      ids should contain(defaultHousehold2.id)
      ids should not contain defaultHousehold3.id
    }
  }

  "getHouseholdById" should {
    "return household by id" in {
      val res = ownersRepo.getHouseholdById(defaultHousehold1.id, Set("viewUnifiedOwnerViaNetwork:LPL")).await
      res.nonEmpty shouldBe true
      res.get.id shouldBe defaultHousehold1.id
    }

    "return None if id doesn't exist" in {
      val res = ownersRepo.getHouseholdById("some id here", userACL.capabilities).await
      res shouldBe None
    }
  }

  "getHouseholdList" should {
    "return basic household aggregation with no associated accounts, profiles and holdings" in {
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      res.flatMap(_.results.map(_.siConcentrationPercentage)).forall(_ == 0.0) shouldBe true

      val hh2 = res.flatMap(_.results.find(_.id == defaultHousehold2.id)).head
      hh2.lnw shouldBe 0
      hh2.profiles shouldBe Seq.empty

      val hh1 = res.flatMap(_.results.find(_.id == defaultHousehold1.id)).head
      hh1.lnw shouldBe 100000
      hh1.profiles shouldBe Seq.empty
    }

    "return basic household aggregation with associated accounts and profiles" in {
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity)).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res.flatMap(_.results.find(_.id == defaultHousehold1.id)).head
      val hh2 = res.flatMap(_.results.find(_.id == defaultHousehold2.id)).head
      hh2.profiles.size shouldBe 0
      hh1.profiles.size shouldBe 1
      hh1.profiles.head.accounts.size shouldBe 1
      hh1.profiles.head.accounts.head.partnerExtensionsLPL shouldBe defaultAccountDetails.partnerExtensionsLPL
    }

    "return basic household aggregation with lnw on a household level" in {
      ownersCollection.drop().toFuture.await
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity, defaultHousehold1)).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh1 = res.flatMap(_.results.find(_.id == defaultHousehold1.id)).head
      hh1.lnw shouldBe defaultHouseholdDetails.liquidNetWorth.get.amount
    }

    "return basic household aggregation with lnw when no liquid net worth on household level but on account" in {
      val householdWithoutLnw = defaultHousehold1.copy(details = defaultHouseholdDetails.copy(liquidNetWorth = None))
      val accountWithLnw = defaultAccount.copy(details = defaultAccountDetails.copy(liquidNetWorthMin = Some(Money(amount = ********, currency = Currency.USD))))
      ownersCollection.drop().toFuture.await
      ownersCollection.insertMany(Seq(accountWithLnw, defaultLegalEntity, householdWithoutLnw)).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh1 = res.flatMap(_.results.find(_.id == defaultHousehold1.id)).head
      hh1.lnw shouldBe ********
    }

    "return basic household aggregation with lnw defaulted to 0 when no lnw on account and household level" in {
      val householdWithoutLnw = defaultHousehold1.copy(details = defaultHouseholdDetails.copy(liquidNetWorth = None))
      ownersCollection.drop().toFuture.await
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity, householdWithoutLnw)).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh1 = res.flatMap(_.results.find(_.id == defaultHousehold1.id)).head
      hh1.lnw shouldBe 0
    }


    "return sorted by id" in {
      val res1 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, None, None, None).await
      res1.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res1.map(_.results.head).head
      hh1.id shouldBe defaultHousehold1.id
      val hh2 = res1.map(_.results.last).last
      hh2.id shouldBe defaultHousehold2.id

      val res2 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Desc, HouseholdSortBy.Id, None, None, None, None).await
      res2.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh11 = res2.map(_.results.head).head
      hh11.id shouldBe defaultHousehold2.id
      val hh22 = res2.map(_.results.last).last
      hh22.id shouldBe defaultHousehold1.id
    }

    "return sorted by lnw" in {
      val res1 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.LNW, None, None, None, None).await
      res1.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res1.map(_.results.head).head
      hh1.lnw shouldBe 0
      val hh2 = res1.map(_.results.last).last
      hh2.lnw shouldBe 100000

      val res2 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Desc, HouseholdSortBy.LNW, None, None, None, None).await
      res2.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh11 = res2.map(_.results.head).head
      hh11.lnw shouldBe 100000
      val hh22 = res2.map(_.results.last).last
      hh22.lnw shouldBe 0
    }

    "return sorted by si concentration" in {
      holdingsCollection.insertOne(defaultHolding).toFuture.await
      val res1 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.SiConcentration, None, None, None, None).await
      res1.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res1.map(_.results.head).head
      hh1.lnw shouldBe 0
      val hh2 = res1.map(_.results.last).last
      hh2.lnw shouldBe 100000

      val res2 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Desc, HouseholdSortBy.LNW, None, None, None, None).await
      res2.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh11 = res2.map(_.results.head).head
      hh11.lnw shouldBe 100000
      val hh22 = res2.map(_.results.last).last
      hh22.lnw shouldBe 0
    }

    "return filtered by si concentration" in {
      holdingsCollection.insertOne(defaultHolding).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, Some(List("10-20")), None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh = res.map(_.results.head).head
      hh.siConcentrationPercentage shouldBe 10
    }

    "return filtered by multiple si concentration" in {
      val holdingForHousehold2 = defaultHolding.copy(ownerAncestorIds = Set(defaultHousehold2.id), marketValue = Some(defaultHolding.marketValue.get.copy(amount = 60000)))
      holdingsCollection.insertMany(Seq(defaultHolding, holdingForHousehold2)).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, Some(List("10-20")), None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh = res.map(_.results.head).head
      hh.siConcentrationPercentage shouldBe 10
      val res2 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, None, Some(List("10-20", "0-10")), None, None).await
      res2.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res2.flatMap(_.results).find(_.id == defaultHousehold1.id)
      val hh2 = res2.flatMap(_.results).find(_.id == defaultHousehold2.id)
      hh1.get.siConcentrationPercentage shouldBe 10
      hh2.get.siConcentrationPercentage shouldBe 0
    }

    "return filtered by lnw" in {
      holdingsCollection.insertOne(defaultHolding).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, Some(List("100000-250000")), None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh = res.map(_.results.head).head
      hh.lnw shouldBe 100000
    }

    "return filtered by multiple lnw" in {
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, Some(List("100000-250000")), None, None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 1
      val hh = res.map(_.results.head).head
      hh.lnw shouldBe 100000

      val res2 = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, Some(List("100000-250000", "0-25000")), None, None, None).await
      res2.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 2
      val hh1 = res2.flatMap(_.results).find(_.id == defaultHousehold1.id)
      val hh2 = res2.flatMap(_.results).find(_.id == defaultHousehold2.id)
      hh1.get.lnw shouldBe 100000
      hh2.get.lnw shouldBe 0
    }

    "return filtered by lnw and si concentration" in {
      holdingsCollection.insertOne(defaultHolding).toFuture.await
      val res = ownersRepo.getHouseholdList(ids = Seq.empty, availableAccessKeys = Set("viewUnifiedOwnerViaNetwork:LPL"), SortOrder.Asc, HouseholdSortBy.Id, Some(List("100000-250000")), Some(List("90")), None, None).await
      res.flatMap(_.totalCount).headOption.fold(0)(_.numberOfItems.toInt) shouldBe 0
    }
  }
}
