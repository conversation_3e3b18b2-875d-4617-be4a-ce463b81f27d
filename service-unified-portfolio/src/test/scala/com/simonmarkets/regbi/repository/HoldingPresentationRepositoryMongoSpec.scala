package com.simonmarkets.regbi.repository

import com.simonmarkets.regbi.TestData
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.unifiedportfolio.domain.data.Holding
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.data.mongo.ops.RegularMongoGenericRepositoryOps._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

import scala.concurrent.ExecutionContext.Implicits.global


class HoldingPresentationRepositoryMongoSpec extends WordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfter with TestData {
  implicit lazy val dbCtx: DatabaseContext = DatabaseContext(db, client)
  private lazy val holdingRepo = new HoldingPresentationRepositoryMongo()
  private lazy val holdingCollection: MongoCollection[Holding] = holdingRepo.mongoOps.getCollection

  before {
    holdingCollection.drop().toFuture.await
    holdingCollection.insertOne(defaultHolding).toFuture.await
  }

  "getHoldingsByHousehold" should {
    "return holding by householdId" in {
      val res = holdingRepo.getHoldingsByHousehold(defaultHousehold1.id, Set("viewUnifiedOwnerViaNetwork:LPL")).await
      res.size shouldBe 1
      res.head.id shouldBe defaultHolding.id
    }

    "return empty list for none existing householdId" in {
      val res = holdingRepo.getHoldingsByHousehold("someId here", Set("viewUnifiedOwnerViaNetwork:LPL")).await
      res.size shouldBe 0
    }
  }
}
