package com.simonmarkets.unifiedportfolio.service

import com.simonmarkets.clients.HttpAddeparClient
import com.simonmarkets.regbi.TestData
import com.simonmarkets.unifiedportfolio.domain.enums.OwnerType.Account
import com.simonmarkets.unifiedportfolio.domain.enums.Partner.Addepar
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AssetClass.{Equity, `Cash & Cash Equivalent`, `Structured Investment`}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.ShallowHoldingView
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.utils.AddeparSampleDataFactory
import com.simonmarkets.unifiedportfolio.domain.requests.{GetHoldingRequest, OwnerListRequest}
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import org.mockito.Mockito.when
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{AsyncWordSpec, Matchers}

import java.util.concurrent.Executors

import scala.concurrent.{ExecutionContext, Future}

class AddeparServiceSpec extends AsyncWordSpec with Matchers with MockitoSugar with ExampleDataFactory with AddeparSampleDataFactory with TestData {

  private val addeparHttpClient = mock[HttpAddeparClient]
  private val serviceCtx = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(1))
  private val adderparService: AddeparServiceImpl = new AddeparServiceImpl(addeparHttpClient)(serviceCtx)


  "getOwners" should {
    "return a list of owners" in {
      val request = OwnerListRequest(Some(Addepar), None, None)
      val response = createAddeparEntitiesResponse(Account, 10)

      when(addeparHttpClient.getEntitiesByType(request)).thenReturn(Future.successful(response))

      adderparService.getOwners(request).map { res =>
        res.size shouldEqual 10
        val accountNames = res.flatMap(_.name)
        accountNames shouldEqual response.data.map(_.attributes.original_name)
      }
    }
  }

  "getHoldings" should {
    "when partner is Addepar" should {
      "return a list of holdings for the account in a simplified format" in {
        val holdingsRequest = GetHoldingRequest("1", Some(Addepar))
        val fakeResponse = createPortfolioQueryResponse(Seq(`Cash & Cash Equivalent`, Equity), 10000.00)
        val expectedResponse = Seq(
          ShallowHoldingView(
            id = "cash",
            marketValue = fakeResponse.data.attributes.total.children.head.columns.value.get,
            displayName = `Cash & Cash Equivalent`.toString
          ),
          ShallowHoldingView(
            id = fakeResponse.data.attributes.total.children.last.children.head.columns.tickerSymbol.get,
            marketValue = fakeResponse.data.attributes.total.children.last.children.head.columns.value.get,
            displayName = fakeResponse.data.attributes.total.children.last.children.head.columns.shortName.get
          )
        )
        when(addeparHttpClient.portfolioQueryById(holdingsRequest)).thenReturn(Future.successful(fakeResponse))

        adderparService.getHoldings(holdingsRequest).map { res =>
          res shouldEqual expectedResponse
        }
      }

      "return a list of holdings for the account using CUSIP instead of ticker for SI" in {
        val holdingsRequest = GetHoldingRequest("1", Some(Addepar))
        val fakeResponse = createPortfolioQueryResponse(Seq(`Structured Investment`), 10000.00)
        val expectedResponse = Seq(
          ShallowHoldingView(
            id = fakeResponse.data.attributes.total.children.last.children.head.columns.cusip.get,
            marketValue = fakeResponse.data.attributes.total.children.last.children.head.columns.value.get,
            displayName = fakeResponse.data.attributes.total.children.last.children.head.columns.shortName.get
          )
        )
        when(addeparHttpClient.portfolioQueryById(holdingsRequest)).thenReturn(Future.successful(fakeResponse))

        adderparService.getHoldings(holdingsRequest).map { res =>
          res shouldEqual expectedResponse
        }
      }

      "return empty response when holdings have no value" in {
        val holdingsRequest = GetHoldingRequest("1", Some(Addepar))
        val fakeResponse = createPortfolioQueryResponse(assetClasses = Seq(`Structured Investment`), totalValue = 0.00)

        when(addeparHttpClient.portfolioQueryById(holdingsRequest)).thenReturn(Future.successful(fakeResponse))

        adderparService.getHoldings(holdingsRequest).map { res =>
          res shouldEqual List.empty
        }
      }
    }
  }
}
