package com.simonmarkets.unifiedportfolio.repository

import com.simonmarkets.regbi.TestData
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.unifiedportfolio.domain.data.{AlternativeDetails, AnnuityDetails, Holding, StructuredInvestmentDetails, TraditionalDetails}
import com.simonmarkets.unifiedportfolio.domain.enums.HoldingType
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository.HoldingRepositoryMongo
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.data.mongo.ops.RegularMongoGenericRepositoryOps._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import org.mongodb.scala.MongoCollection
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

import scala.concurrent.ExecutionContext.Implicits.global


class HoldingRepositorySpec extends WordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfter with ExampleDataFactory with TestData {
  implicit lazy val dbCtx: DatabaseContext = DatabaseContext(db, client)
  private lazy val holdingsRepo = new HoldingRepositoryMongo()
  private lazy val holdingsCollection: MongoCollection[Holding] = holdingsRepo.mongoOps.getCollection
  private lazy val holdingAccessKeys = Set(s"viewUnifiedHoldingViaNetwork:$networkId")

  before {
    holdingsCollection.drop().toFuture.await
  }

  "get" should {

    "return None with a bad id" in {
      val result = holdingsRepo.get("nothing here", holdingAccessKeys).await
      result.shouldBe(None)
    }

    "fail if access keys are invalid" in {
      val account = exampleAccount()
      val inputHolding = exampleSiHolding(account, 1, Set(account.id))
      holdingsCollection.insertOne(inputHolding).toFuture.await
      val result = holdingsRepo.get(inputHolding.id, Set("spectacular", "spiderman")).await
      result shouldBe None
    }


    "return an SI holding by id" in {
      val account = exampleAccount()
      val inputHolding = exampleSiHolding(account, 1, Set(account.id))
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val result = holdingsRepo.get(inputHolding.id, holdingAccessKeys).await
      val returnedHolding = result.head

      returnedHolding.id shouldBe inputHolding.id
      returnedHolding.holdingType shouldBe HoldingType.StructuredInvestment
      returnedHolding.auth shouldBe inputHolding.auth
      returnedHolding.details shouldBe a[StructuredInvestmentDetails]
    }

    "return an Alt holding by id" in {
      val account = exampleAccount()
      val inputHolding = exampleAltsHolding(account, 1, Set(account.id))
      val inputDeets = inputHolding.details.asInstanceOf[AlternativeDetails]
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val result = holdingsRepo.get(inputHolding.id, holdingAccessKeys).await
      val returnedHolding = result.head
      val returnedDeets = returnedHolding.details.asInstanceOf[AlternativeDetails]

      returnedHolding.id shouldBe inputHolding.id
      returnedHolding.holdingType shouldBe HoldingType.AlternativeInvestment
      returnedHolding.auth shouldBe inputHolding.auth
      returnedDeets shouldBe inputDeets
    }

    "return an Annuity holding by id" in {
      val account = exampleAccount()
      val inputHolding = exampleAnnuitiesHolding(account, 1, Set(account.id))
      val inputDeets = inputHolding.details.asInstanceOf[AnnuityDetails]
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val result = holdingsRepo.get(inputHolding.id, holdingAccessKeys).await
      val returnedHolding = result.head
      val returnedDeets = returnedHolding.details.asInstanceOf[AnnuityDetails]

      returnedHolding.id shouldBe inputHolding.id
      returnedHolding.holdingType shouldBe HoldingType.Annuity
      returnedHolding.auth shouldBe inputHolding.auth
      returnedDeets shouldBe inputDeets
    }

    "return a Traditional holding by id" in {
      val account = exampleAccount()
      val inputHolding = exampleTraditionalHolding(account, 1, Set(account.id))
      val inputDeets = inputHolding.details.asInstanceOf[TraditionalDetails]
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val result = holdingsRepo.get(inputHolding.id, holdingAccessKeys).await
      val returnedHolding = result.head
      val returnedDeets = returnedHolding.details.asInstanceOf[TraditionalDetails]

      returnedHolding.id shouldBe inputHolding.id
      returnedHolding.holdingType shouldBe HoldingType.TraditionalInvestment
      returnedHolding.auth shouldBe inputHolding.auth
      returnedDeets shouldBe inputDeets
    }

  }

  "getSiHoldingsByOwnerWithoutCapability" should {
    "return all holdings associated with a certain owner" in {
      val account = exampleAccount()
      val inputHolding = exampleSiHolding(account, 1, Set(account.id))
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val res = holdingsRepo.getSiHoldingsByOwnerWithoutCapability(account.id).await
      res.length shouldBe 1
      res.head.ownerAncestorIds should contain(account.id)
    }

    "return empty list if no association" in {
      val account = exampleAccount()
      val inputHolding = exampleSiHolding(account, 1, Set(account.id))
      holdingsCollection.insertOne(inputHolding).toFuture.await

      val res = holdingsRepo.getSiHoldingsByOwnerWithoutCapability("randomId").await
      res.length shouldBe 0
    }
  }

}
