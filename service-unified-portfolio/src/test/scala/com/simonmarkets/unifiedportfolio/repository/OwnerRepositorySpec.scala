package com.simonmarkets.unifiedportfolio.repository

import com.simonmarkets.regbi.TestData
import com.simonmarkets.unifiedportfolio.utils.ExampleDataFactory
import com.simonmarkets.syntax.futureOpsConversion
import com.simonmarkets.unifiedportfolio.domain.data.{AccountDetails, HouseholdDetails, LegalEntityDetails, Owner, Person}
import com.simonmarkets.unifiedportfolio.domain.enums.{LegalEntityType, OwnerType}
import com.simonmarkets.unifiedportfolio.repository.OwnerRepository.OwnerRepositoryMongo
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import com.simonmarkets.utils.data.mongo.ops.RegularMongoGenericRepositoryOps._
import com.simonmarkets.utils.test.mongo.EmbeddedMongoLike
import io.simon.encryption.v2.model.EncryptedData
import org.mongodb.scala.MongoCollection
import org.scalatest.{BeforeAndAfter, Matchers, WordSpec}

import scala.concurrent.ExecutionContext.Implicits.global


class OwnerRepositorySpec extends WordSpec with EmbeddedMongoLike with Matchers with BeforeAndAfter with ExampleDataFactory with TestData {
  implicit lazy val dbCtx: DatabaseContext = DatabaseContext(db, client)
  private lazy val ownersRepo = new OwnerRepositoryMongo()
  private lazy val ownersCollection: MongoCollection[Owner] = ownersRepo.mongoOps.getCollection
  private lazy val ownerAccessKeys = Set(s"viewUnifiedOwnerViaNetwork:$networkId", s"editUnifiedOwnerViaNetwork:$networkId")

  before {
    ownersCollection.drop().toFuture.await
  }

  "get" should {

    "return None with a bad id" in {
      val result = ownersRepo.get("nothing here", ownerAccessKeys).await
      result.shouldBe(None)
    }

    "fail if access keys are invalid" in {
      val inputHousehold = exampleHousehold()
      ownersCollection.insertOne(inputHousehold).toFuture.await
      val result = ownersRepo.get(inputHousehold.id, Set("spectacular", "spiderman")).await
      result shouldBe None
    }

    "return a Household by id" in {
      val inputHousehold = exampleHousehold()
      val inputDeets = inputHousehold.details.asInstanceOf[HouseholdDetails]
      ownersCollection.insertOne(inputHousehold).toFuture.await

      val result = ownersRepo.get(inputHousehold.id, ownerAccessKeys).await
      val returnedHousehold = result.head
      val returnedDeets = returnedHousehold.details.asInstanceOf[HouseholdDetails]

      returnedHousehold.id shouldBe inputHousehold.id
      returnedHousehold.ownerType shouldBe OwnerType.Household
      returnedHousehold.auth shouldBe inputHousehold.auth
      returnedDeets.annualExpenses shouldBe inputDeets.annualExpenses
      returnedDeets.obligations shouldBe inputDeets.obligations
    }

    "return a person LegalEntity by id" in {
      val inputPerson = examplePerson()
      val inputDeets = inputPerson.details.asInstanceOf[LegalEntityDetails]
      val inputEntity = inputDeets.entity.asInstanceOf[Person]
      ownersCollection.insertOne(inputPerson).toFuture.await

      val result = ownersRepo.get(inputPerson.id, ownerAccessKeys).await
      val returnedPerson = result.head
      val returnedDeets = returnedPerson.details.asInstanceOf[LegalEntityDetails]
      val returnedEntity = returnedDeets.entity.asInstanceOf[Person]

      returnedPerson.id shouldBe inputPerson.id
      returnedPerson.ownerType shouldBe OwnerType.LegalEntity
      returnedPerson.auth shouldBe inputPerson.auth
      returnedDeets.taxId.head shouldBe a[EncryptedData]
      returnedDeets.entityType shouldBe LegalEntityType.Person
      returnedEntity.productTypes shouldBe inputEntity.productTypes
      returnedEntity.age shouldBe inputEntity.age
      returnedEntity.experience shouldBe  inputEntity.experience
    }

    "return an Account by id" in {
      val inputAccount = exampleAccount()
      val inputDeets = inputAccount.details.asInstanceOf[AccountDetails]
      ownersCollection.insertOne(inputAccount).toFuture.await

      val result = ownersRepo.get(inputAccount.id, ownerAccessKeys).await
      val returnedAccount = result.head
      val returnedDeets = returnedAccount.details.asInstanceOf[AccountDetails]

      returnedAccount.id shouldBe inputAccount.id
      returnedAccount.ownerType shouldBe OwnerType.Account
      returnedAccount.auth shouldBe inputAccount.auth
      returnedDeets.documents shouldBe inputDeets.documents
      returnedDeets.accountNumber.head shouldBe a[EncryptedData]
      returnedDeets.accountType shouldBe inputDeets.accountType
      returnedDeets.investmentObjective shouldBe inputDeets.investmentObjective
      returnedDeets.totalValue shouldBe inputDeets.totalValue
    }
  }

  "upsert" should {
    "update existing owner" in {
      val inputHousehold = exampleHousehold()
      ownersCollection.insertOne(inputHousehold).toFuture.await
      val res = ownersCollection.find().toFuture().await
      res.head.parentIds shouldBe Set.empty
      val updatedHousehold = inputHousehold.copy(parentIds = Set("1", "2"))
      ownersRepo.upsert(updatedHousehold, ownerAccessKeys).await
      val updatedRes = ownersCollection.find().toFuture().await
      updatedRes.head.parentIds shouldBe Set("1", "2")
      updatedRes.head.id shouldBe inputHousehold.id
    }
  }

  "getAccountsLNWByHouseholdWithoutCapabilities" should {
    "return all accounts lnw associated with a certain household" in {
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity, defaultHousehold1)).toFuture.await
      val res = ownersRepo.getAccountsLNWByHouseholdWithoutCapabilities(defaultHousehold1.id).await
      res.size shouldBe 1
      res.head.liquidNetWorthMin shouldBe defaultAccount.details.asInstanceOf[AccountDetails].liquidNetWorthMin
      res.head.liquidNetWorthMax shouldBe defaultAccount.details.asInstanceOf[AccountDetails].liquidNetWorthMax
    }
  }

  "getOwnerByTypeWithoutCapabilities" should {
    "return owner by type without capabilities" in {
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity, defaultHousehold1)).toFuture.await
      val householdRes = ownersRepo.getOwnerByTypeWithoutCapabilities(defaultHousehold1.id, OwnerType.Household).await
      val accountRes = ownersRepo.getOwnerByTypeWithoutCapabilities(defaultAccount.id, OwnerType.Account).await
      val legalEntityRes = ownersRepo.getOwnerByTypeWithoutCapabilities(defaultLegalEntity.id, OwnerType.LegalEntity).await
      val wrongTypeRes = ownersRepo.getOwnerByTypeWithoutCapabilities(defaultLegalEntity.id, OwnerType.Household).await

      householdRes.map(owner => owner.id shouldBe defaultHousehold1.id)
      accountRes.map(owner => owner.id shouldBe defaultAccount.id)
      legalEntityRes.map(owner => owner.id shouldBe defaultLegalEntity.id)
      wrongTypeRes shouldBe None
    }
  }

  "getOwnerParentWithoutCapabilities" should {
    "get owner parent by ownerId and parent type" in {
      ownersCollection.insertMany(Seq(defaultAccount, defaultLegalEntity, defaultHousehold1)).toFuture.await
      val accountParent = ownersRepo.getOwnerParentWithoutCapabilities(defaultAccount.parentIds, OwnerType.LegalEntity).await
      val legalEntityParent = ownersRepo.getOwnerParentWithoutCapabilities(defaultLegalEntity.parentIds, OwnerType.Household).await

      accountParent.map(owner => owner.id shouldBe defaultLegalEntity.id)
      legalEntityParent.map(owner => owner.id shouldBe defaultHousehold1.id)

    }
  }
}
