package com.simonmarkets.utils

import com.simonmarkets.logging.TraceId
import io.simon.encryption.v2.model.EncryptedData
import io.simon.encryption.v3.service.IcnEncryptionService
import simon.Id.NetworkId
import io.simon.encryption.v3.conversion.{EncryptionSearch => v3Search, EncryptionWriter => v3Writer, EncryptionReader => v3Reader}

import scala.concurrent.Future

trait IcnEncryptionHelper {

  val encryptionService: IcnEncryptionService

  def constructKey[K](fieldName: String, index: K): String = s"$fieldName:$index"

  private def getEncryptedData[A, ID](values: Map[ID, A], networkId: NetworkId, includeHash: Boolean)
    (implicit write: v3Writer[A], search: v3Search[A], traceId: TraceId): Future[Map[ID, EncryptedData]] = {
    if (values.isEmpty)
      Future.successful(Map.empty[ID, EncryptedData])
    else
      encryptionService.bulkEncrypt[A, ID](values, networkId, includeHash)
  }

  def getEncryptedDataOpt[A, ID](values: Map[ID, Option[A]], networkId: NetworkId, includeHash: Boolean = true)
    (implicit w: v3Writer[A], s: v3Search[A], tid: TraceId): Future[Map[ID, EncryptedData]] =
    getEncryptedData(
      values.collect { case (key, Some(value)) => (key, value) },
      networkId,
      includeHash)

  private def getDecryptedData[A, ID](values: Map[ID, EncryptedData])
    (implicit reader: v3Reader[A], traceId: TraceId): Future[Map[ID, A]] = {
    if (values.isEmpty)
      Future.successful(Map.empty[ID, A])
    else
      encryptionService.bulkDecrypt[A, ID](values)
  }

  def getDecryptedDataOpt[A, ID](values: Map[ID, Option[EncryptedData]])
    (implicit reader: v3Reader[A], traceId: TraceId): Future[Map[ID, A]] =
    getDecryptedData(values.collect { case (key, Some(value)) => (key, value) })

}
