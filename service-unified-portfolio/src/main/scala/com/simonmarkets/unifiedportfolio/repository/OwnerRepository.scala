package com.simonmarkets.unifiedportfolio.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.unifiedportfolio.domain.data.{Money, Owner}
import com.simonmarkets.unifiedportfolio.domain.enums.OwnerType
import com.simonmarkets.unifiedportfolio.repository.OwnerRepository.AccountLnw
import com.simonmarkets.utils.data.core.GenericRepository
import com.simonmarkets.utils.data.mongo.SimonMongoRepository
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import org.mongodb.scala.bson.{BsonArray, Document}
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Aggregates.{lookup, project}
import org.mongodb.scala.model.{Filters, FindOneAndReplaceOptions, Variable}
import org.mongodb.scala.model.Filters.{equal, expr, in}
import org.mongodb.scala.model.Projections.{computed, fields, include}
import org.mongodb.scala.model.Aggregates._
import org.mongodb.scala.result.InsertManyResult

import scala.concurrent.{ExecutionContext, Future}

trait OwnerRepository extends GenericRepository[Owner] {
  def upsert(owner: Owner, availableAccessKeys: Set[String]): Future[Unit]

  def getOwnerByTypeWithoutCapabilities(id: String, ownerType: OwnerType)(implicit traceId: TraceId): Future[Option[Owner]]

  def getOwnerParentWithoutCapabilities(parentIds: Set[String], ownerType: OwnerType): Future[Option[Owner]]

  def getAccountsLNWByHouseholdWithoutCapabilities(householdId: String): Future[Seq[AccountLnw]]
}

object OwnerRepository {

  class OwnerRepositoryMongo(implicit ec: ExecutionContext,
      dbCtx: DatabaseContext) extends SimonMongoRepository[Owner] with OwnerRepository {

    private lazy val collection = dbCtx.getCollection[Owner]("owners").withCodecRegistry(Owner.codecRegistry)

    def clearAllData: Future[Void] = {
      collection.drop().toFuture()
    }

    // Inserts the records without checking perms (in the context of an initial database seed
    // using example data).
    def seedRecords(owners: List[Owner]): Future[InsertManyResult] = {
      collection.insertMany(owners).toFuture()
    }

    override def upsert(owner: Owner, availableAccessKeys: Set[String]): Future[Unit] = {
      collection.findOneAndReplace(
        Filters.and(equal("id", owner.id), accessKeysCondition(availableAccessKeys)),
        owner,
        options = new FindOneAndReplaceOptions().upsert(true)
      ).completeWithUnit.toFuture
    }

    override def getOwnerByTypeWithoutCapabilities(id: String, ownerType: OwnerType)(implicit traceId: TraceId): Future[Option[Owner]] = {
      val filters = Seq(equal("id", id), equal("ownerType", ownerType))
      collection.find[Owner](Filters.and(filters: _*)).comment(traceId.traceId).headOption
    }

    override def getOwnerParentWithoutCapabilities(parentIds: Set[String], ownerType: OwnerType): Future[Option[Owner]] = {
      val filters = Seq(in("id", parentIds.toSeq: _*), equal("ownerType", ownerType))
      collection.find[Owner](Filters.and(filters: _*)).headOption
    }

    override def getAccountsLNWByHouseholdWithoutCapabilities(householdId: String): Future[Seq[AccountLnw]] = {

      val accountsLookUp = lookup(
        from = "owners",
        let = Seq(Variable("legalEntityId", "$id")),
        pipeline = Seq(
          `match`(
            Filters.and(
              equal("ownerType", OwnerType.Account),
              expr(Document("$in" -> BsonArray("$$legalEntityId", "$parentIds")))
            )
          ),
          project(
            fields(
              computed("liquidNetWorthMin", "$details.liquidNetWorthMin"),
              computed("liquidNetWorthMax", "$details.liquidNetWorthMax")
            )
          )
        ),
        as = "accounts"
      )

      val profilesLookUp = lookup(
        from = "owners",
        let = Seq(Variable("householdId", "$id")),
        pipeline = Seq(
          `match`(
            Filters.and(
              equal("ownerType", OwnerType.LegalEntity),
              expr(Document("$in" -> BsonArray("$$householdId", "$parentIds")))
            )
          ),
          project(
            fields(
              include("id"),
            )
          ),
          accountsLookUp
        ),
        as = "profiles"
      )

      val unwindStage = Seq(unwind("$profiles"), unwind("$profiles.accounts"), replaceRoot("$profiles.accounts"))

      collection.aggregate[AccountLnw](Seq(`match`(Filters.and(equal("id", householdId), equal("ownerType", OwnerType.Household))), profilesLookUp) ++ unwindStage).toFuture()
    }
  }

  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)

  case class AccountLnw(
      liquidNetWorthMin: Option[Money],
      liquidNetWorthMax: Option[Money],
  )

}

