package com.simonmarkets.unifiedportfolio.repository

import com.simonmarkets.logging.TraceId
import com.simonmarkets.unifiedportfolio.domain.data.Holding
import com.simonmarkets.unifiedportfolio.domain.enums.HoldingType
import com.simonmarkets.utils.data.core.GenericRepository
import com.simonmarkets.utils.data.mongo.SimonMongoRepository
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import org.mongodb.scala.model.Filters
import org.mongodb.scala.model.Filters.equal
import org.mongodb.scala.result.InsertManyResult

import scala.concurrent.{ExecutionContext, Future}

trait HoldingRepository extends GenericRepository[Holding] {
  def getSiHoldingsByOwnerWithoutCapability(ownerId: String)(implicit traceId: TraceId): Future[Seq[Holding]]
}

object HoldingRepository {
  class HoldingRepositoryMongo(implicit ec: ExecutionContext,
      dbCtx: DatabaseContext) extends SimonMongoRepository[Holding] with HoldingRepository {

    private lazy val collection = dbCtx.getCollection[Holding]("holdings").withCodecRegistry(Holding.codecRegistry)

    def clearAllData: Future[Void] = {
      collection.drop().toFuture()
    }

    // Inserts the records without checking perms (in the context of an initial database seed
    // using example data).
    def seedRecords(holdings: List[Holding]): Future[InsertManyResult] = {
      collection.insertMany(holdings).toFuture()
    }

    override def getSiHoldingsByOwnerWithoutCapability(ownerId: String)(implicit traceId: TraceId): Future[Seq[Holding]] = {
      collection.find(
        Filters.and(
          equal("holdingType", HoldingType.StructuredInvestment),
          Filters.in("ownerAncestorIds", ownerId)
        )
      ).comment(traceId.traceId).toFuture
    }
  }
}
