package com.simonmarkets.unifiedportfolio.service

import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.UnifiedHoldingsCapabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.unifiedportfolio.domain.data.{AlternativeDetails, AnnuityDetails, Holding, HoldingDetails, StructuredInvestmentDetails, TraditionalDetails}
import com.simonmarkets.unifiedportfolio.domain.requests.GetHoldingRequest
import com.simonmarkets.unifiedportfolio.domain.view.{AlternativeDetailsView, AnnuityDetailsView, HoldingDetailsView, HoldingView, OwnershipView, StructuredInvestmentDetailsView, TraditionalDetailsView}
import com.simonmarkets.unifiedportfolio.repository.HoldingRepository

import scala.concurrent.{ExecutionContext, Future}

trait HoldingService {
  /** Returns the Holding by given search parameters, if present */
  def get(request: GetHoldingRequest)(implicit traceId: TraceId, user: UserACL): Future[Option[HoldingView]]
}

object HoldingService {
  class HoldingServiceImpl(
      repository: HoldingRepository,
  )
    (implicit executionContext: ExecutionContext) extends HoldingService with TraceLogging {

    private def detailsView(details: HoldingDetails): HoldingDetailsView = {
      details match {
        case AlternativeDetails(shareClassId, _) => {
          AlternativeDetailsView(shareClassId)
        }
        case AnnuityDetails(totalContractAmount, surrenderValue, totalPremium, policyNumber, productName, carrierKey, annuityAssetClass, _) => {
          AnnuityDetailsView(totalContractAmount, surrenderValue, totalPremium, policyNumber, productName, carrierKey, annuityAssetClass)
        }
        case StructuredInvestmentDetails(_) => {
          StructuredInvestmentDetailsView()
        }
        case TraditionalDetails(ticker, _) => {
          TraditionalDetailsView(ticker)
        }
      }
    }

    private def holdingView(h: Holding): HoldingView = HoldingView(
      h.id,
      h.holdingType,
      h.productId,
      h.marketValue,
      h.totalCommitment,
      h.withdrawableAmount,
      h.ownerships.map(o => OwnershipView(o.ownerId, Some("Decrypted Name"), o.ownerType, o.stake)),
      detailsView(h.details),
    )

    /** Returns the Holding by given search parameters, if present */
    override def get(request: GetHoldingRequest)
      (implicit traceId: TraceId, user: UserACL): Future[Option[HoldingView]] = {
      val availableKeys = UnifiedHoldingsCapabilities.getAvailableAccessKeysForCapabilities(UnifiedHoldingsCapabilities.ViewCapabilities, user)
      repository
        .get(request.id, availableKeys)
        .map(holdingOpt => holdingOpt.map(holdingView))
    }
  }

  implicit class FutureOptOps[A](val future: Future[Option[A]]) extends AnyVal {
    /** Returns the flattened future, or NotFound if the value is empty */
    def getOrNotFound(implicit ec: ExecutionContext): Future[A] = {
      future.flatMap(opt => opt.map(a => Future.successful(a)).getOrElse(Future.failed(HttpError.notFound())))
    }
  }
}
