package com.simonmarkets.unifiedportfolio.service

import akka.http.scaladsl.model.StatusCodes.UnprocessableEntity
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.capabilities.UnifiedOwnersCapabilities
import com.simonmarkets.http.HttpError
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.unifiedportfolio.domain.data._
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.LPLAccountPrimaryClassification
import com.simonmarkets.unifiedportfolio.domain.enums.{ExperienceProductType, OwnerType}
import com.simonmarkets.unifiedportfolio.domain.requests.{DeleteOwnerRequest, GetOwnerRequest, OwnersPatchRequest}
import com.simonmarkets.unifiedportfolio.domain.view.{AccountDetailsView, HouseholdDetailsView, LegalEntityDetailsView, LegalEntityView, OwnerDetailsView, OwnerView, PersonView}
import com.simonmarkets.unifiedportfolio.repository.{HoldingRepository, OwnerRepository}

import java.time.LocalDateTime

import scala.concurrent.{ExecutionContext, Future}

trait OwnerService {
  /** Returns the Owner by given search parameters, if present */
  def get(request: GetOwnerRequest)(implicit traceId: TraceId, user: UserACL): Future[Option[OwnerView]]

  /** Deletes the given Owner, if present */
  def delete(request: DeleteOwnerRequest)(implicit traceId: TraceId, user: UserACL): Future[Boolean]

  def ownerPatch(id: String, req: OwnersPatchRequest)(implicit traceId: TraceId, user: UserACL): Future[Unit]

  def getRulesEngineInfo(accountId: String)(implicit traceId: TraceId, user: UserACL): Future[RulesInfoResponse]
}

object OwnerService {
  class OwnerServiceImpl(ownerRepo: OwnerRepository, holdingRepo: HoldingRepository)
    (implicit executionContext: ExecutionContext) extends OwnerService with TraceLogging {

    override def getRulesEngineInfo(accountId: String)(implicit traceId: TraceId, user: UserACL): Future[RulesInfoResponse] = {
      for {
        targetAccount <- ownerRepo.getOwnerByTypeWithoutCapabilities(accountId, OwnerType.Account).getOrNotFound
        accountHoldings <- holdingRepo.getSiHoldingsByOwnerWithoutCapability(targetAccount.id)
        targetLegalEntity <- ownerRepo.getOwnerParentWithoutCapabilities(targetAccount.parentIds, OwnerType.LegalEntity)
        targetHousehold <- targetLegalEntity match {
          case Some(entity) => ownerRepo.getOwnerParentWithoutCapabilities(entity.parentIds, OwnerType.Household)
          case _ => Future.successful(None)
        }
        accountDetails = targetAccount.details.asInstanceOf[AccountDetails]
        householdDetails = targetHousehold.map(hh => hh.details.asInstanceOf[HouseholdDetails])
        legalEntityDetails = targetLegalEntity.map(le => le.details.asInstanceOf[LegalEntityDetails])
        lnw <- targetHousehold match {
          case Some(houseHold) => getMaxHouseholdLNW(houseHold)
          case _ => Future.successful(accountDetails.liquidNetWorthMin.getOrElse(Money.defaultMoney).amount)
        }
        experience = legalEntityDetails.map(details => details.entity.asInstanceOf[Person].productTypes.filter(p => ExperienceProductType.parList.contains(p.productType)).map(_.years).max)
      } yield RulesInfoResponse(
        accountId = accountId,
        lnw = lnw,
        isSAM = accountDetails.partnerExtensionsLPL.exists(a => a.primaryClassification == LPLAccountPrimaryClassification.SAM),
        documents = accountDetails.documents,
        holdings = accountHoldings.map(holding => HoldingInfo(cusip = holding.id, amount = holding.totalCommitment.getOrElse(Money.defaultMoney).amount)),
        investmentObjective = accountDetails.investmentObjective,
        experienceSI = experience,
        experiencePAR = experience,
        rType = accountDetails.partnerExtensionsLPL.map(a => a.rType.productPrefix),
        annualExpenses = householdDetails.flatMap(hh => hh.annualExpenses.map(h => h.amount)),
        availableLCF = householdDetails.flatMap(hh => hh.availableLCF.map(h => h.amount)),
        obligations = householdDetails.flatMap(hh => hh.obligations.map(h => h.amount))
      )
    }

    private def getMaxHouseholdLNW(houseHold: Owner): Future[Int] = {
      val householdDetails = houseHold.details.asInstanceOf[HouseholdDetails]
      if (householdDetails.liquidNetWorth.isDefined)
        Future.successful(householdDetails.liquidNetWorth.getOrElse(Money.defaultMoney).amount)
      else {
        implicit val moneyOrdering: Ordering[Money] = Ordering.by(_.amount)
        for {
          a <- ownerRepo.getAccountsLNWByHouseholdWithoutCapabilities(houseHold.id)
          _ = println(a.size)
          _ = println(a.size)
          _ = println(a.size)
        } yield ()
        ownerRepo.getAccountsLNWByHouseholdWithoutCapabilities(houseHold.id).map(
          accounts => accounts.maxBy(acc => acc.liquidNetWorthMax)
            .liquidNetWorthMin.getOrElse(Money.defaultMoney).amount)
      }
    }


    override def ownerPatch(id: String, req: OwnersPatchRequest)(implicit traceId: TraceId, user: UserACL): Future[Unit] = {
      val availableAccessKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.EditCapabilities, user)

      for {
        owner <- ownerRepo.get(id, availableAccessKeys).getOrNotFound
        _ <- validatePatchRequest(owner.ownerType, req)
        updatedDetails <- owner.details match {
          case householdDetails: HouseholdDetails => patchUpdateHousehold(householdDetails, req)
          case legalEntityDetails: LegalEntityDetails => patchUpdateLegalEntity(legalEntityDetails, req)
          case _ =>
            log.error(s"Not supported LegalEntity type - ${owner.ownerType}")
            Future.failed(HttpError(UnprocessableEntity, s"Not supported LegalEntity type - ${owner.ownerType}"))
        }
        updatedOwner = owner.copy(timestamps = owner.timestamps.copy(updatedAt = LocalDateTime.now()), details = updatedDetails)
        _ <- ownerRepo.upsert(updatedOwner, availableAccessKeys)
      } yield ()
    }

    private def patchUpdateLegalEntity(legalEntityDetails: LegalEntityDetails, request: OwnersPatchRequest)(implicit traceId: TraceId): Future[OwnerDetails] = {
      legalEntityDetails.entity match {
        case personEntity: Person => {
          request.legalEntityPersonPatchFields match {
            case Some(fields) => {
              val productTypes = fields.productTypes match {
                case NotProvided => personEntity.productTypes
                case Clear => Set[ProductType]()
                case SetTo(value) => value
              }
              Future.successful(legalEntityDetails.copy(entity = personEntity.copy(productTypes = productTypes)))
            }
            case _ => Future.successful(legalEntityDetails)
          }
        }
        case _ =>
          log.error(s"Not supported LegalEntity type - ${legalEntityDetails.entityType}")
          Future.failed(HttpError(UnprocessableEntity, s"Not supported LegalEntity type - ${legalEntityDetails.entityType}"))
      }
    }

    private def patchUpdateHousehold(householdDetails: HouseholdDetails, request: OwnersPatchRequest): Future[OwnerDetails] = {
      request.householdDetailsPatchFields match {
        case Some(fields) => {
          val liquidNetWorth = PatchField.resolveOptPatch(fields.liquidNetWorth, householdDetails.liquidNetWorth)
          val annualExpenses = PatchField.resolveOptPatch(fields.annualExpenses, householdDetails.annualExpenses)
          val availableLCF = PatchField.resolveOptPatch(fields.availableLCF, householdDetails.availableLCF)
          val obligations = PatchField.resolveOptPatch(fields.obligations, householdDetails.obligations)
          Future.successful(householdDetails.copy(
            liquidNetWorth = liquidNetWorth,
            annualExpenses = annualExpenses,
            availableLCF = availableLCF,
            obligations = obligations
          ))
        }
        case _ => Future.successful(householdDetails)
      }
    }

    private def validatePatchRequest(ownerType: OwnerType, patchRequest: OwnersPatchRequest)(implicit traceId: TraceId): Future[Unit] = {
      val errMsg = s"Can't patch owner with ownerType - $ownerType and this request $patchRequest"
      ownerType match {
        case OwnerType.Household => {
          patchRequest match {
            case OwnersPatchRequest(Some(_), None) => Future.unit
            case _ =>
              log.error(errMsg)
              Future.failed(HttpError(UnprocessableEntity, errMsg))
          }
        }
        case OwnerType.LegalEntity => {
          // we have only one Legal entity type for now so it makes no sense to check inner legal entity type here
          patchRequest match {
            case OwnersPatchRequest(None, Some(_)) => Future.unit
            case _ =>
              log.error(errMsg)
              Future.failed(HttpError(UnprocessableEntity, errMsg))
          }
        }
        case _ => Future.failed(HttpError(UnprocessableEntity, s"Patch endpoint only supports household and LegalEntity ownerType fields but found $ownerType"))
      }
    }

    private def ownerDetailsView(details: OwnerDetails): OwnerDetailsView = {
      details match {
        case HouseholdDetails(liquidNetWorthMin, liquidNetWorthMax, liquidNetWorth, annualExpenses, availableLCF, obligations, _) => {
          HouseholdDetailsView(liquidNetWorthMin, liquidNetWorthMax, liquidNetWorth, annualExpenses, availableLCF, obligations)
        }
        case AccountDetails(accountType, accountNumber@_, investmentObjective, totalValue, documents, partnerExtensionsLPL, lnwMin, lnwMax, _) => {
          // TODO replace with real decryption
          AccountDetailsView(accountType, Some("DecryptedAccountNumber"), investmentObjective, totalValue, documents, partnerExtensionsLPL, lnwMin, lnwMax)
        }
        case LegalEntityDetails(taxId@_, entityType, entity, _) => {
          val entityView: LegalEntityView = entity match {
            case Person(experience, age, productTypes, _) => {
              PersonView(experience, age, productTypes)
            }
          }
          // TODO replace with real decryption
          LegalEntityDetailsView(Some("DecryptedTaxId"), entityType, entityView)
        }
      }
    }

    private def ownerView(o: Owner): OwnerView = OwnerView(
      o.id,
      o.ownerType,
      Some("Decrypted Name"),
      o.parentIds,
      ownerDetailsView(o.details),
    )


    /** Returns the Holding by given search parameters, if present */
    override def get(request: GetOwnerRequest)
      (implicit traceId: TraceId, user: UserACL): Future[Option[OwnerView]] = {

      val availableKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.ViewCapabilities, user)
      val views = ownerRepo
        .get(request.id, availableKeys)
        .map(ownerOpt => ownerOpt.map(ownerView))
      views
    }

    override def delete(request: DeleteOwnerRequest)
      (implicit traceId: TraceId, user: UserACL): Future[Boolean] = {

      val availableKeys = UnifiedOwnersCapabilities.getAvailableAccessKeysForCapabilities(UnifiedOwnersCapabilities.EditCapabilities, user)
      ownerRepo.delete(request.id, availableKeys)
    }
  }

  implicit class FutureOptOps[A](val future: Future[Option[A]]) extends AnyVal {
    /** Returns the flattened future, or NotFound if the value is empty */
    def getOrNotFound(implicit ec: ExecutionContext): Future[A] = {
      future.flatMap(opt => opt.map(a => Future.successful(a)).getOrElse(Future.failed(HttpError.notFound())))
    }
  }
}
