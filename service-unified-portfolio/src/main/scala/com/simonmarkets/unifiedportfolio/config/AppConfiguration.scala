package com.simonmarkets.unifiedportfolio.config

import com.simonmarkets.api.users.config.AclClientConfig
import com.simonmarkets.resteasy.framework.RunMode
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration
import com.simonmarkets.resteasy.framework.config.RestEasyAppConfiguration.SystemRoutesConfig
import com.simonmarkets.resteasy.framework.config.authn.AuthenticationConfiguration
import com.simonmarkets.resteasy.framework.info.InfoConfig
import com.simonmarkets.mongodb.config.MongoClientConfig
import io.simon.encryption.v3.service.IcnEncryptionService.IcnEncryptionConfig

case class AppConfiguration(
    runMode: RunMode,
    systemRoutes: Option[SystemRoutesConfig],
    authentication: Option[AuthenticationConfiguration],
    aclClientConfig: AclClientConfig,
    systemUserId: String,
    info: Option[InfoConfig],
    mongoDB: MongoConfiguration,
    icnEncryption: IcnEncryptionConfig,
    snowflake: SnowflakeConfig,
    addepar: AddeparConfig
) extends RestEasyAppConfiguration

case class AddeparConfig(
    baseUrl: String
)

case class DbConfig(
    collection: String,
    database: String
)

case class MongoConfiguration(
    owner: DbConfig,
    holding: DbConfig,
    client: MongoClientConfig
)

case class SnowflakeConfig(
    url: String,
    user: String,
    role: String,
    warehouse: String,
    privateKey: String,
)
