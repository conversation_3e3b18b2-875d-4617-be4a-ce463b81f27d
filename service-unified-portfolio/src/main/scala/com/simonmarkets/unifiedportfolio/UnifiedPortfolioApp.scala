package com.simonmarkets.unifiedportfolio

import com.simonmarkets.unifiedportfolio.config.AppConfiguration
import com.simonmarkets.unifiedportfolio.di.ServiceLocator
import com.simonmarkets.unifiedportfolio.routes.UnifiedPortfolioRoutes
import com.simonmarkets.resteasy.framework.{Environment, RestEasyModule}
import io.simon.openapi.generator.OpenApiGenerator
import pureconfig.generic.auto._

object UnifiedPortfolioApp extends RestEasyModule[AppConfiguration] with App {
  override def serviceName: String = "UnifiedPortfolio"

  override def servicePath: String = "/simon/api/v1/unified-portfolio"

  override def init(environment: Environment): Unit = {
    val locator = new ServiceLocator(config)
    val routes = new UnifiedPortfolioRoutes(locator.authDirective,
      locator.ownerService, locator.holdingService, locator.regBiPresentationService, locator.addeparService).routes
    environment.addRoutes(routes)
  }

  OpenApiGenerator.generateOpenApiDocumentation()
  RestEasy.start()

}
