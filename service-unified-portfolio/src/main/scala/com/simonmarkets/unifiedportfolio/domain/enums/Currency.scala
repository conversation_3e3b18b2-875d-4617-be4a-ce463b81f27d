package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait Currency extends EnumEntry

object Currency extends ProductEnums[Currency] {

  case object EUR extends Currency
  case object GBP extends Currency
  case object USD extends Currency

  override def Values: Seq[Currency] = Seq(
    EUR,
    GBP,
    USD,
  )

  @EnumValues(
    "EUR",
    "GBP",
    "USD",
  )
  case object Ref extends Reference
}

