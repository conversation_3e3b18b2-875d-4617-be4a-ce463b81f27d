package com.simonmarkets.unifiedportfolio.domain.view

import com.simonmarkets.unifiedportfolio.domain.data.{Money, ProductId}
import com.simonmarkets.unifiedportfolio.domain.enums.HoldingType

case class HoldingView(
    id: String,
    holdingType: HoldingType,
    productId: ProductId,
    marketValue: Option[Money],
    totalCommitment: Option[Money],
    withdrawableAmount: Option[Money],
    ownerships: Set[OwnershipView],
    details: HoldingDetailsView,
)