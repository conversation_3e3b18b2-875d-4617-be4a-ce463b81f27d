package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait LegalEntityType extends EnumEntry

object LegalEntityType extends ProductEnums[LegalEntityType] {

  case object Corporation extends LegalEntityType
  case object Person extends LegalEntityType
  case object Trust extends LegalEntityType

  override def Values: Seq[LegalEntityType] = Seq(
    Corporation,
    Person,
    Trust,
  )

  @EnumValues(
    "Corporation",
    "Person",
    "Trust",
  )
  case object Ref extends Reference
}

