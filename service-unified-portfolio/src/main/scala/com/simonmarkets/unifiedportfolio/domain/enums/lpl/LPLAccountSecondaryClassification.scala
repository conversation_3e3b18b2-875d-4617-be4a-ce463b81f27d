package com.simonmarkets.unifiedportfolio.domain.enums.lpl

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait LPLAccountSecondaryClassification extends EnumEntry

object LPLAccountSecondaryClassification extends ProductEnums[LPLAccountSecondaryClassification] {

  case object BBK extends LPLAccountSecondaryClassification
  case object QBK extends LPLAccountSecondaryClassification
  case object ASM extends LPLAccountSecondaryClassification
  case object RSM extends LPLAccountSecondaryClassification
  case object AS2 extends LPLAccountSecondaryClassification
  case object RS2 extends LPLAccountSecondaryClassification
  case object A1H extends LPLAccountSecondaryClassification
  case object R1H extends LPLAccountSecondaryClassification
  case object A2H extends LPLAccountSecondaryClassification
  case object R2H extends LPLAccountSecondaryClassification

  override def Values: Seq[LPLAccountSecondaryClassification] = Seq(
    BBK,
    QBK,
    ASM,
    RSM,
    AS2,
    RS2,
    A1H,
    R1H,
    A2H,
    R2H,
  )

  @EnumValues(
    "BBK",
    "QBK",
    "ASM",
    "RSM",
    "AS2",
    "RS2",
    "A1H",
    "R1H",
    "A2H",
    "R2H",
  )
  case object Ref extends Reference
}

