package com.simonmarkets.unifiedportfolio.domain.data

import com.simonmarkets.unifiedportfolio.domain.enums.LegalEntityType
import io.simon.encryption.v2.model.EncryptedData

sealed trait OwnerDetails

case class AccountDetails(
    accountType: Option[String], // E.g. "Retirement", "Individual"
    accountNumber: Option[EncryptedData],
    investmentObjective: Option[String],
    totalValue: Option[Money],
    documents: Set[String],
    partnerExtensionsLPL: Option[LPLAccountExtensions] = None,
    liquidNetWorthMin: Option[Money],
    liquidNetWorthMax: Option[Money],
    _t: String = AccountDetails.getClass.getName
) extends OwnerDetails

case class HouseholdDetails(
    liquidNetWorthMin: Option[Money],
    liquidNetWorthMax: Option[Money],
    liquidNetWorth: Option[Money] = None,
    annualExpenses: Option[Money],
    availableLCF: Option[Money],
    obligations: Option[Money],
    _t: String = HouseholdDetails.getClass.getName
) extends OwnerDetails

case class LegalEntityDetails(
    taxId: Option[EncryptedData],
    entityType: LegalEntityType,
    entity: LegalEntity,
    _t: String = LegalEntityDetails.getClass.getName
) extends OwnerDetails


