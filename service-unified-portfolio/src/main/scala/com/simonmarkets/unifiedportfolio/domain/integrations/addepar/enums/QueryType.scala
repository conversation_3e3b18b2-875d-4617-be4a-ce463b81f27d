package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}

sealed trait QueryType extends EnumEntry {
  def value: String
}

object QueryType extends ProductEnums[QueryType] {
  case object PortfolioQuery extends QueryType {
    override def value: String = "portfolio_query"
  }

  case object PortfolioViews extends QueryType {
    override def value: String = "portfolio_views"
  }


  override def apply(value: String): QueryType = {
    Values.find(_.value == value).getOrElse {
      throw new IllegalArgumentException(s"Unknown QueryType value: $value")
    }
  }
  override def Values: Seq[QueryType] = Seq(PortfolioQuery, PortfolioViews)
}
