package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait AssetClass extends EnumEntry

object AssetClass extends ProductEnums[AssetClass] {
  case object `Cash & Cash Equivalent` extends AssetClass

  case object Cash extends AssetClass

  case object `Loan Receivable` extends AssetClass

  case object `Fixed Income` extends AssetClass

  case object Credit extends AssetClass

  case object Equity extends AssetClass

  case object `Hedge Fund` extends AssetClass

  case object `Private Equity` extends AssetClass

  case object `Real Estate` extends AssetClass

  case object Alternative extends AssetClass

  case object Art extends AssetClass

  case object Derivative extends AssetClass

  case object Mortgage extends AssetClass

  case object `Real Assets` extends AssetClass

  case object `Family Business` extends AssetClass

  case object `Venture Capital` extends AssetClass

  case object Other extends AssetClass

  case object `Structured Investment` extends AssetClass

  override def Values: Seq[AssetClass] = Seq(
    `Cash & Cash Equivalent`,
    Cash,
    `Loan Receivable`,
    `Fixed Income`,
    Credit,
    Equity,
    `Hedge Fund`,
    `Private Equity`,
    `Real Estate`,
    Alternative,
    Art,
    Derivative,
    Mortgage,
    `Real Assets`,
    `Family Business`,
    `Venture Capital`,
    Other,
    `Structured Investment`
  )

  @EnumValues(
    "Cash & Cash Equivalent",
    "Cash",
    "Loan Receivable",
    "Fixed Income",
    "Credit",
    "Equity",
    "Hedge Fund",
    "Private Equity",
    "Real Estate",
    "Alternative",
    "Art",
    "Derivative",
    "Mortgage",
    "Real Assets",
    "Family Business",
    "Venture Capital",
    "Other",
    "Structured Investment"
  )
  case object Ref extends Reference
}
