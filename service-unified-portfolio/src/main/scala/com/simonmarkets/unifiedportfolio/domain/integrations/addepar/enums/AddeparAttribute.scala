package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}

sealed trait AddeparAttribute extends EnumEntry {
  def value: String
}

object AddeparAttribute extends ProductEnums[AddeparAttribute] {
  case object Ticker extends AddeparAttribute {
    override def value = "ticker_symbol"
  }

  case object ShortName extends AddeparAttribute {
    override def value = "short_name"
  }

  case object AssetClass extends AddeparAttribute {
    override def value = "asset_class"
  }

  case object Security extends AddeparAttribute {
    override def value = "security"
  }

  case object OriginalName extends AddeparAttribute {
    override def value: String = "original_name"
  }

  case object FinancialAccount extends AddeparAttribute {
    override def value: String = "financial_account"
  }

  case object Value extends AddeparAttribute {
    override def value: String = "value"
  }

  override def apply(value: String): AddeparAttribute = {
    Values.find(_.value == value).getOrElse {
      throw new IllegalArgumentException(s"Unknown AddeparAttribute value: $value")
    }
  }
  override def Values: Seq[AddeparAttribute] = Seq(Ticker, ShortName, AssetClass, Security, OriginalName, FinancialAccount, Value)
}
