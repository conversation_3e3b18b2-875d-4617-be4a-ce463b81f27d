package com.simonmarkets.unifiedportfolio.domain.data

import com.simonmarkets.unifiedportfolio.domain.enums.OwnerType
import io.simon.encryption.v2.model.EncryptedData

// This will be attached to Owners rather than saved as a top level document in its
// own Collection.  It can contain information about the relationship between
// the Owner and the Holding, but can also contain denormalized summary information
// about the Owner.
case class Ownership(
    ownerId: String,
    name: EncryptedData,
    ownerType: OwnerType,
    stake: Option[Double] // percent ownership, e.g. 0.5
)
