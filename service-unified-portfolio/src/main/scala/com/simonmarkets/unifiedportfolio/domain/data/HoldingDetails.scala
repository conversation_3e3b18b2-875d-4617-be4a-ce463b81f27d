package com.simonmarkets.unifiedportfolio.domain.data

sealed trait HoldingDetails

case class StructuredInvestmentDetails(
    // ...  TODO more SI-specific attributes, inspired by SI model
    _t: String = StructuredInvestmentDetails.getClass.getName
) extends HoldingDetails

// Inspired by Annuities service.
// Note: not utilizing the "Money" type here because this model has a very large number of
// monetary fields, and each given Annuity will be only a single currency.  It's redundant to store
// 25 additional copies of "USD" per record.  Our API's should probably return this info as Money
// objects though.
case class AnnuityDetails(
    totalContractAmount: Option[Int],
    surrenderValue: Option[Int],
    totalPremium: Option[Int],
    policyNumber: String,
    productName: Option[String],
    carrierKey: Option[String],
    annuityAssetClass: String,
    // ... TODO more attributes from Annuities service.
    _t: String = AnnuityDetails.getClass.getName
) extends HoldingDetails

case class AlternativeDetails(
    shareClassId: String,
    // ... TODO more alt-specific attributes from DLT
    _t: String = AlternativeDetails.getClass.getName
) extends HoldingDetails

case class TraditionalDetails(
    ticker: String,
    // ... more traditional-specific attributes
    _t: String = TraditionalDetails.getClass.getName
) extends HoldingDetails
