package com.simonmarkets.unifiedportfolio.domain.view

sealed trait HoldingDetailsView

case class StructuredInvestmentDetailsView(
) extends HoldingDetailsView

case class AlternativeDetailsView(
    shareClassId: String,
) extends HoldingDetailsView

case class AnnuityDetailsView(
    totalContractAmount: Option[Int],
    surrenderValue: Option[Int],
    totalPremium: Option[Int],
    policyNumber: String,
    productName: Option[String],
    carrierKey: Option[String],
    annuityAssetClass: String,
) extends HoldingDetailsView

case class TraditionalDetailsView(
    ticker: String,
) extends HoldingDetailsView

