package com.simonmarkets.unifiedportfolio.domain.data

case class RulesInfoResponse(
    accountId: String,
    lnw: Int,
    isSAM: Boolean,
    documents: Set[String],
    holdings: Seq[HoldingInfo],
    investmentObjective: Option[String],
    experienceSI: Option[Int],
    experiencePAR: Option[Int],
    rType: Option[String],
    annualExpenses: Option[Int],
    availableLCF: Option[Int],
    obligations: Option[Int],
)

case class HoldingInfo(
    cusip: String,
    amount: Int
)
