package com.simonmarkets.unifiedportfolio.domain.data

sealed trait PatchField[+T]

case object NotProvided extends PatchField[Nothing]

case object Clear extends PatchField[Nothing]

case class SetTo[T](value: T) extends PatchField[T]

object PatchField {
  def resolveOptPatch[A](patch: <PERSON>Field[A], current: Option[A]): Option[A] = patch match {
    case NotProvided => current
    case Clear => None
    case SetTo(v) => Some(v)
  }
}
