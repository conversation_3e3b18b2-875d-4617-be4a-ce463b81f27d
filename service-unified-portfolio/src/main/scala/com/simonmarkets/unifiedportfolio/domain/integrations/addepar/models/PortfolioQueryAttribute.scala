package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models

import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AddeparAttribute

case class PortfolioQueryAttribute(
    key: AddeparAttribute,
    arguments: Option[AddeparAttributeArguments] = None
)

case class AddeparAttributeArguments(
    valuationAdjustmentType: Option[String],
    negativeValueHandling: Option[String],
    timePoint: Option[String],
    accrued: Option[String],
    currency: Option[String],
)

object AddeparAttributeArguments {
  def default: AddeparAttributeArguments = {
    AddeparAttributeArguments(
      valuationAdjustmentType = Some("none"),
      negativeValueHandling = Some("show_as_negative"),
      timePoint = Some("current"),
      accrued = Some("all"),
      currency = Some("USD")
    )
  }
}
