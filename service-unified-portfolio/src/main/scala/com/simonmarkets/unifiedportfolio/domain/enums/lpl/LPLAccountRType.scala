package com.simonmarkets.unifiedportfolio.domain.enums.lpl

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait LPLAccountRType extends EnumEntry

object LPLAccountRType extends ProductEnums[LPLAccountRType] {

  // NOTE: there may be more that we haven't seen as of this writing.
  case object F extends LPLAccountRType
  case object FS extends LPLAccountRType

  override def Values: Seq[LPLAccountRType] = Seq(
    F,
    FS,
  )

  @EnumValues(
    "F",
    "FS",
  )
  case object Ref extends Reference
}

