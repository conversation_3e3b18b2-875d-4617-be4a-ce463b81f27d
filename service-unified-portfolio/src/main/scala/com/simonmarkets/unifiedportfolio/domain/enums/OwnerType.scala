package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait OwnerType extends EnumEntry

object OwnerType extends ProductEnums[OwnerType] {

  case object Account extends OwnerType
  case object LegalEntity extends OwnerType
  case object Household extends OwnerType

  override def Values: Seq[OwnerType] = Seq(
    Account,
    LegalEntity,
    Household
  )

  @EnumValues(
    "Account",
    "LegalEntity",
    "Household",
  )
  case object Ref extends Reference
}

