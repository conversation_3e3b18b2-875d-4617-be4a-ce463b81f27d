package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses

import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AddeparAttribute
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AssetClass.{Cash, `Cash & Cash Equivalent`, `Structured Investment`}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.ShallowHoldingView

case class PortfolioQueryResponse(
    data: PortfolioQueryResponseData
) {
  def toGroupedHoldingView: Seq[ShallowHoldingView] = {
    data.attributes.total.children.flatMap { assetClassLevelData =>
      if (assetClassLevelData.name == `Cash & Cash Equivalent`.toString || assetClassLevelData.name == Cash.toString ) {
        Seq(ShallowHoldingView(
          id = "cash",
          marketValue = assetClassLevelData.columns.value.getOrElse(0.0),
          displayName = assetClassLevelData.name
        ))
      }
      else {
        assetClassLevelData.children.map { assetClassHolding =>
          val tickerOpt = if (assetClassHolding.columns.shortName.contains(`Structured Investment`.toString))
            assetClassHolding.columns.cusip
          else assetClassHolding.columns.tickerSymbol

          ShallowHoldingView(
            id = tickerOpt.getOrElse(""),
            marketValue = assetClassHolding.columns.value.getOrElse(0.0),
            displayName = assetClassHolding.columns.shortName.getOrElse("")
          )
        }
      }
    }.filterNot(_.marketValue == 0.0)
  }
}


case class PortfolioQueryResponseData(
    attributes: PortfolioQueryResponseAttributes
)

case class PortfolioQueryResponseAttributes(
    total: PortfolioQueryTotalSummary
)

case class PortfolioQueryTotalSummary(
    name: String,
    /** These columns are equivalent to the columns we request in the PortfolioQueryRequest payload.
     * Note: they will also be present in each of the children but will be mostly null at the top (total) level.
     */
    columns: PortfolioQueryDataColumns,
    children: Seq[PortfolioQueryDataChild]
)

/** This object must match the same columns we supply in [[PortfolioQueryRequest.data.attributes]] */
case class PortfolioQueryDataColumns(
    tickerSymbol: Option[String],
    value: Option[Double],
    shortName: Option[String],
    cusip: Option[String],
)

case class PortfolioQueryDataChild(
    name: String,
    grouping: AddeparAttribute,
    columns: PortfolioQueryDataColumns,
    //Used to nest results when grouping by multiple things
    children: Seq[PortfolioQueryDataChild],
)
