package com.simonmarkets.unifiedportfolio.domain.view

import com.simonmarkets.unifiedportfolio.domain.data.{LPLAccountExtensions, Money}
import com.simonmarkets.unifiedportfolio.domain.enums.LegalEntityType

sealed trait OwnerDetailsView

case class HouseholdDetailsView(
    liquidNetWorthMin: Option[Money],
    liquidNetWorthMax: Option[Money],
    liquidNetWorth: Option[Money],
    annualExpenses: Option[Money],
    availableLCF: Option[Money],
    obligations: Option[Money],
) extends OwnerDetailsView

case class AccountDetailsView(
    accountType: Option[String], // E.g. "SAM", "SWM"
    accountNumber: Option[String],
    investmentObjective: Option[String],
    totalValue: Option[Money],
    documents: Set[String],
    partnerExtensionsLPL: Option[LPLAccountExtensions],
    liquidNetWorthMin: Option[Money],
    liquidNetWorthMax: Option[Money],
) extends OwnerDetailsView

case class LegalEntityDetailsView(
    taxId: Option[String],
    entityType: LegalEntityType,
    entity: LegalEntityView,
) extends OwnerDetailsView

