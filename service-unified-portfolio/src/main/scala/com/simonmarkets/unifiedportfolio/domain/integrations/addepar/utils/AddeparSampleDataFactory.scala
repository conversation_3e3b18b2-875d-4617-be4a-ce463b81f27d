package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.utils

import com.simonmarkets.unifiedportfolio.domain.enums.OwnerType
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AddeparAttribute.Security
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.{AddeparAttribute, AssetClass}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.{AddeparAccessToken, AddeparFirm}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses.{AddeparEntitiesAttributes, AddeparEntitiesData, AddeparEntitiesResponse, PortfolioQueryDataChild, PortfolioQueryDataColumns, PortfolioQueryResponse, PortfolioQueryResponseAttributes, PortfolioQueryResponseData, PortfolioQueryTotalSummary}

import scala.util.Random

trait AddeparSampleDataFactory {
  lazy implicit val accessToken: AddeparAccessToken = AddeparAccessToken("someToken")
  lazy implicit val addeparFirm: AddeparFirm = AddeparFirm("someFirm")


  private def randomAlphaString(length: Int, random: scala.util.Random): String = {
    val chars = ('A' to 'Z') ++ ('a' to 'z')
    (1 to length).map(_ => chars(random.nextInt(chars.length))).mkString
  }

  private def randomAlphaNumericString(length: Int, random: scala.util.Random): String = {
    val chars = ('A' to 'Z') ++ ('a' to 'z') ++ ('0' to '9')
    (1 to length).map(_ => chars(random.nextInt(chars.length))).mkString
  }

  private def randomCusip(random: scala.util.Random): String = {
    randomAlphaNumericString(9, random)
  }

  private def randomShortName(name: String, random: scala.util.Random): String = {
    s"$name ${randomAlphaString(2, random).toUpperCase}"
  }

  //Portfolio Query Response Methods for generating sample Holdings
  private def fakePortfolioQueryDataColumns(
      tickerSymbol: Option[String] = None,
      value: Option[Double],
      shortName: Option[String] = None,
      cusip: Option[String] = None
  ): PortfolioQueryDataColumns =
    PortfolioQueryDataColumns(
      tickerSymbol,
      value,
      shortName,
      cusip
    )

  def createPortfolioQueryResponse(
      assetClasses: Seq[AssetClass],
      totalValue: Double,
      randomSeed: Option[Long] = None
  ): PortfolioQueryResponse = {
    val random = randomSeed.map(new scala.util.Random(_)).getOrElse(new scala.util.Random())

    val assetClassValues = if (assetClasses.isEmpty) {
      Seq.empty
    } else {
      val rawValues = assetClasses.map(_ => random.nextDouble())
      val sum = rawValues.sum
      val normalizedValues = rawValues.map(v => v / sum * totalValue)
      assetClasses.zip(normalizedValues)
    }

    val assetClassChildren = assetClassValues.map { case (assetClass, value) =>
      val roundedValue = BigDecimal(value).setScale(2, BigDecimal.RoundingMode.HALF_UP).toDouble

      if (assetClass == AssetClass.`Cash & Cash Equivalent`) {
        val cashChild = PortfolioQueryDataChild(
          name = "USD",
          grouping = Security,
          columns = fakePortfolioQueryDataColumns(
            value = Some(roundedValue),
            tickerSymbol = None,
            shortName = None,
            cusip = None
          ),
          children = Seq.empty,
        )

        PortfolioQueryDataChild(
          name = assetClass.toString,
          grouping = AddeparAttribute.AssetClass,
          columns = fakePortfolioQueryDataColumns(value = Some(roundedValue)),
          children = Seq(cashChild),
        )
      } else {
          val name = random.alphanumeric.toString
          val ticker = if (assetClass == AssetClass.`Structured Investment`) "" else randomCusip(random)
          val cusip = randomCusip(random)
          val shortName = randomShortName(name, random)

          val child = PortfolioQueryDataChild(
            name = name,
            grouping = Security,
            columns = fakePortfolioQueryDataColumns(
              value = Some(roundedValue),
              tickerSymbol = if (ticker.isEmpty) Some(cusip) else Some(ticker),
              shortName = Some(shortName),
              cusip = Some(cusip)
            ),
            children = Seq.empty,
          )

        PortfolioQueryDataChild(
          name = assetClass.toString,
          grouping = AddeparAttribute.AssetClass,
          columns = fakePortfolioQueryDataColumns(value = Some(roundedValue)),
          children = Seq(child),
        )
      }
    }

    PortfolioQueryResponse(
      data = PortfolioQueryResponseData(
        attributes = PortfolioQueryResponseAttributes(
          total = PortfolioQueryTotalSummary(
            name = "Total",
            columns = fakePortfolioQueryDataColumns(value = Some(totalValue)),
            children = assetClassChildren
          )
        )
      )
    )
  }


  //Entities Response for generating sample Accounts (and other OwnerTypes in the future)

  private def fakeAddeparEntitiesData(ownerType: OwnerType): AddeparEntitiesData = AddeparEntitiesData(
    id = Random.nextInt.toString,
    `type` = ownerType.toString,
    attributes = AddeparEntitiesAttributes(Random.nextString(10))
  )

  def createAddeparEntitiesResponse(ownerType: OwnerType, numAccounts: Int): AddeparEntitiesResponse = {
   val accounts: Seq[AddeparEntitiesData] = Range(0, numAccounts).map { _ =>
     fakeAddeparEntitiesData(ownerType)
   }
    AddeparEntitiesResponse(accounts)
  }
}
