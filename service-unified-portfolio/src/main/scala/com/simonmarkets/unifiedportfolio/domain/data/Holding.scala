package com.simonmarkets.unifiedportfolio.domain.data

import com.simonmarkets.entitlements.AcceptedAccessKeysGenerator
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.resteasy.utils.SimonCodecs
import com.simonmarkets.unifiedportfolio.access.HoldingKeysGenerator
import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, HoldingType, OwnerType}
import com.simonmarkets.utils.data.mongo.model.SimonResourceCompanion
import io.simon.encryption.v2.model.EncryptedData
import org.bson.codecs.configuration.CodecProvider
import org.mongodb.scala.bson.codecs.Macros._

case class Holding(
    id: String,
    acceptedAccessKeys: Set[String], // necessary at top level for resteasy framework
    holdingType: HoldingType,
    productId: ProductId,

    marketValue: Option[Money],
    totalCommitment: Option[Money],
    withdrawableAmount: Option[Money],

    // This field will hold summary information about only the IMMEDIATE owners.
    ownerships: Set[Ownership],
    // The following field needs to exist to efficiently support filtering by ownership.
    // For example, Architect may want a list of all Holdings within a specified Household.
    // An index on the values of this array, which holds a flattened list of all Owners in the heirarchy
    // for the holding, will make this efficient regardless of the depth of the ownership tree.
    ownerAncestorIds: Set[String],

    auth: Authorization,
    timestamps: RecordTimestamps,

    details: HoldingDetails,
)



object Holding extends SimonResourceCompanion[Holding] {
  override def collectionName: String = "holdings"

  override def resourceName: String = "Holding"

  override def accessKeyGenerator: AcceptedAccessKeysGenerator[Holding] = HoldingKeysGenerator

  override def codecProviders: List[CodecProvider] = List(
    createCodecProviderIgnoreNone[Holding],
    createCodecProviderIgnoreNone[Authorization],
    createCodecProviderIgnoreNone[EncryptedData],
    createCodecProviderIgnoreNone[ExternalId],
    classOf[HoldingDetails],
    createCodecProviderIgnoreNone[Money],
    createCodecProviderIgnoreNone[Ownership],
    createCodecProviderIgnoreNone[ProductId],
    createCodecProviderIgnoreNone[RecordTimestamps],
    EnumEntryCodecProvider[Currency],
    EnumEntryCodecProvider[HoldingType],
    EnumEntryCodecProvider[OwnerType],
    SimonCodecs.SimonIdProvider,
  )
}
