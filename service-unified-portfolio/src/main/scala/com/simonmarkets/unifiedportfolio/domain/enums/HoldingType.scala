package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait HoldingType extends EnumEntry

object HoldingType extends ProductEnums[HoldingType] {

  case object AlternativeInvestment extends HoldingType
  case object Annuity extends HoldingType
  case object StructuredInvestment extends HoldingType
  case object TraditionalInvestment extends HoldingType

  override def Values: Seq[HoldingType] = Seq(
    AlternativeInvestment,
    Annuity,
    StructuredInvestment,
    TraditionalInvestment,
  )

  @EnumValues(
    "AlternativeInvestment",
    "Annuity",
    "StructuredInvestment",
    "TraditionalInvestment",
  )
  case object Ref extends Reference
}

