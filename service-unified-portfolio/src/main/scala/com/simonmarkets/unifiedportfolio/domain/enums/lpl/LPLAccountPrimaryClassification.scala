package com.simonmarkets.unifiedportfolio.domain.enums.lpl

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait LPLAccountPrimaryClassification extends EnumEntry

object LPLAccountPrimaryClassification extends ProductEnums[LPLAccountPrimaryClassification] {

  case object Brokerage extends LPLAccountPrimaryClassification
  case object SAM extends LPLAccountPrimaryClassification
  case object SWM extends LPLAccountPrimaryClassification

  override def Values: Seq[LPLAccountPrimaryClassification] = Seq(
    Brokerage,
    SAM,
    SWM,
  )

  @EnumValues(
    "Brokerage",
    "SAM",
    "SWM",
  )
  case object Ref extends Reference
}

