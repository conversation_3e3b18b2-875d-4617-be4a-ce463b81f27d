package com.simonmarkets.unifiedportfolio.domain.requests

import com.simonmarkets.regbi.domain.OpenApiDefinitions.{Limit, Offset}
import com.simonmarkets.unifiedportfolio.domain.enums.Partner
import io.simon.openapi.annotation.Field.Ref

case class OwnerListRequest(
    @Ref(Partner.Ref)
    partner: Option[Partner],
    @Ref(Offset)
    offset: Option[Int],
    @Ref(Limit)
    limit: Option[Int],
  )
