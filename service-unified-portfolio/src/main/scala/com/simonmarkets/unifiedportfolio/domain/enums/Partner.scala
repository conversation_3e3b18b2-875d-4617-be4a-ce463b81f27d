package com.simonmarkets.unifiedportfolio.domain.enums

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait Partner extends EnumEntry

object Partner extends ProductEnums[Partner] {

  case object Addepar extends Partner

  @EnumValues("addepar")
  override def Values: Seq[Partner] = Seq(Addepar)

  case object Ref extends Reference
}
