package com.simonmarkets.unifiedportfolio.domain.requests

import com.simonmarkets.unifiedportfolio.domain.data.{Money, NotProvided, PatchField, ProductType}

case class OwnersPatchRequest(
    householdDetailsPatchFields: Option[HouseholdDetailsPatchFields] = None,
    legalEntityPersonPatchFields: Option[LegalEntityPersonPatchFields] = None
)

case class HouseholdDetailsPatchFields(
    liquidNetWorth: PatchField[Money] = NotProvided,
    annualExpenses: PatchField[Money] = NotProvided,
    availableLCF: PatchField[Money] = NotProvided,
    obligations: PatchField[Money] = NotProvided
)

case class LegalEntityPersonPatchFields(
    productTypes: PatchField[Set[ProductType]] = NotProvided
)