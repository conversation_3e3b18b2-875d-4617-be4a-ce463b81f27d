package com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses

import com.simonmarkets.unifiedportfolio.domain.enums.OwnerType.Account
import com.simonmarkets.unifiedportfolio.domain.view.OwnershipView

case class AddeparEntitiesResponse(
    data: Seq[AddeparEntitiesData]
) {
  val toOwnershipViews: Seq[OwnershipView] = {
    data.map { entityData =>
      OwnershipView(
        id = entityData.id,
        name = Some(entityData.attributes.original_name),
        //TODO - should be derived from the response
        ownerType = Account,
        stake = None
      )
    }
  }
}

case class AddeparEntitiesData(
    id: String,
    `type`: String,
    attributes: AddeparEntitiesAttributes
)

case class AddeparEntitiesAttributes(
    original_name: String
)
