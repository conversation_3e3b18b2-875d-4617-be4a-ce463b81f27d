package com.simonmarkets.unifiedportfolio.access

import com.simonmarkets.capabilities.{Capabilities, UnifiedOwnersCapabilities}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.unifiedportfolio.domain.data.Owner

object OwnerKeysGenerator extends AcceptedAccessKeysGenerator[Owner] {
  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[Owner]] = Map(
    Capabilities.Admin -> AcceptedKeyBuilder(buildAdminKeys),
    UnifiedOwnersCapabilities.ViewUnifiedOwnerViaNetwork -> AcceptedKeyBuilder(networkKeys),
    UnifiedOwnersCapabilities.EditUnifiedOwnerViaNetwork -> AcceptedKeyBuilder(networkKeys),
  )

  private def networkKeys(capability: String, resource: Owner): Set[String] = {
    Set(s"$capability:${resource.auth.networkId}")
  }
}