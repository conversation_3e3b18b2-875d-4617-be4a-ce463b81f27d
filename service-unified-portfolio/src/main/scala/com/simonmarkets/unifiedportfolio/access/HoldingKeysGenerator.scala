package com.simonmarkets.unifiedportfolio.access

import com.simonmarkets.capabilities.{Capabilities, UnifiedHoldingsCapabilities}
import com.simonmarkets.entitlements.{AcceptedAccessKeysGenerator, AcceptedKeyBuilder}
import com.simonmarkets.unifiedportfolio.domain.data.Holding

object HoldingKeysGenerator extends AcceptedAccessKeysGenerator[Holding] {
  override def capabilityToAcceptedKeyBuilders: Map[String, AcceptedKeyBuilder[Holding]] = Map(
    Capabilities.Admin -> AcceptedKeyBuilder(buildAdminKeys),
    UnifiedHoldingsCapabilities.ViewUnifiedHoldingViaNetwork -> AcceptedKeyBuilder(networkKeys),
  )

  private def networkKeys(capability: String, resource: Holding): Set[String] = {
    Set(s"$capability:${resource.auth.networkId}")
  }
}