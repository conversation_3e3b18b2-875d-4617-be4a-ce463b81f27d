package com.simonmarkets.unifiedportfolio.routes

import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.unifiedportfolio.domain.data.{Clear, PatchField, SetTo}
import com.simonmarkets.unifiedportfolio.domain.requests.{HouseholdDetailsPatchFields, LegalEntityPersonPatchFields, OwnersPatchRequest}
import com.simonmarkets.unifiedportfolio.domain.view.{HoldingDetailsView, LegalEntityView, OwnerDetailsView}
import io.circe._
import io.circe.generic.extras.Configuration
import io.circe.generic.extras.semiauto._

trait UpsCirceCodecs extends JsonCodecs {
  def polymorphicConfig: Configuration = Configuration.default.withDiscriminator("type").copy(
    transformConstructorNames =
      s => {
        s.takeRight(4) match {
          case "View" => s.dropRight(4)
          case _ => s
        }
      }
  )
  implicit val ownerDetailsEncoder: Encoder[OwnerDetailsView] = {
    implicit val config: Configuration = polymorphicConfig
    deriveConfiguredEncoder
  }
  implicit val holdingDetailsEncoder: Encoder[HoldingDetailsView] = {
    implicit val config: Configuration = polymorphicConfig
    deriveConfiguredEncoder
  }

  implicit val legalEntityEncoder: Encoder[LegalEntityView] = {
    implicit val config: Configuration = polymorphicConfig
    deriveConfiguredEncoder
  }


  implicit def patchFieldDecoder[T: Decoder]: Decoder[PatchField[T]] = Decoder.instance { cursor =>
    cursor.value match {
      case Json.Null => Right(Clear)
      case _ => Decoder[T].apply(cursor).map(SetTo(_))
    }
  }

  implicit val householdDetailsPatchFieldsDecoder: Decoder[HouseholdDetailsPatchFields] = {
    implicit val customConfig: Configuration = Configuration.default.withDefaults
    deriveConfiguredDecoder[HouseholdDetailsPatchFields]
  }

  implicit val legalEntityPersonPatchFieldsDecoder: Decoder[LegalEntityPersonPatchFields] = {
    implicit val customConfig: Configuration = Configuration.default.withDefaults
    deriveConfiguredDecoder[LegalEntityPersonPatchFields]
  }

  implicit val ownersPatchRequestDecoder: Decoder[OwnersPatchRequest] = {
    implicit val customConfig: Configuration = Configuration.default.withDefaults
    deriveConfiguredDecoder
  }
}