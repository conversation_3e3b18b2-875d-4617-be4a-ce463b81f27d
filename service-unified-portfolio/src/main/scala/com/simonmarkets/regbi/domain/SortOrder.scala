package com.simonmarkets.regbi.domain

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed abstract class SortOrder(val intValue: Int) extends EnumEntry

@EnumValues("asc", "desc")
object SortOrder extends ProductEnums[SortOrder] {
  case object Asc extends SortOrder(1)
  case object Desc extends SortOrder(-1)

  override def Values: Seq[SortOrder] = Seq(Asc, Desc)

  @EnumValues("asc", "desc")
  case object Ref extends Reference
}
