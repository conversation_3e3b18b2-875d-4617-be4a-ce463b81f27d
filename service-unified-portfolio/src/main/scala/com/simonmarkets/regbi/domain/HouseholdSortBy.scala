package com.simonmarkets.regbi.domain

import com.simonmarkets.util.{EnumEntry, ProductEnums}
import io.simon.openapi.annotation.Field.EnumValues
import io.simon.openapi.annotation.Reference

sealed trait HouseholdSortBy extends EnumEntry

object HouseholdSortBy extends ProductEnums[HouseholdSortBy] {
  case object Name extends HouseholdSortBy
  case object Id extends HouseholdSortBy
  case object LNW extends HouseholdSortBy
  case object SiConcentration extends HouseholdSortBy

  override def Values: Seq[HouseholdSortBy] = Seq(Name, Id, LNW, SiConcentration)

  @EnumValues("name", "id", "lnw", "siConcentration")
  case object Ref extends Reference
}

