package com.simonmarkets.regbi.domain

import io.simon.openapi.annotation.Field.{MaxLength, Maximum, MinLength, Minimum, Type}
import io.simon.openapi.annotation.{OpenApiType, Reference}

object OpenApiDefinitions {
  @Type(OpenApiType.Int)
  @Minimum(0)
  case object Offset extends Reference

  @Type(OpenApiType.Int)
  @Minimum(1)
  @Maximum(100)
  case object Limit extends Reference

  @Type(OpenApiType.String)
  @MinLength(3)
  @MaxLength(25)
  case object SearchPattern extends Reference
}
