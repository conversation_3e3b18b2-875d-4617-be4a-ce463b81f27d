package com.simonmarkets.regbi.service

import com.simonmarkets.regbi.domain.TestSnowflakeUser
import com.simonmarkets.unifiedportfolio.config.SnowflakeConfig
import com.snowflake.snowpark.Session

import scala.concurrent.{ExecutionContext, Future}


class SnowflakeProvider(config: Map[String, String])(implicit ec: ExecutionContext) {

  private def closableSnowflakeSession[T](f: Session => T): T = {
    lazy val session: Session = Session.builder.configs(config).create
    try {
      f(session)
    } finally {
      session.close()
    }
  }

  def getTestData: Future[List[TestSnowflakeUser]] = {
    Future {
      closableSnowflakeSession { session =>
//        session.sql("USE DATABASE DEV_UNIFIED_PORTFOLIO_TEST_DB").collect()
//        session.sql("USE SCHEMA TEST_SCHEMA").collect()
//        session.sql("CREATE OR REPLACE STREAM DUMMY_USERS_STREAM ON TABLE DUMMY_USERS APPEND_ONLY = FALSE").collect()
        session.sql("SELECT * FROM DEV_UNIFIED_PORTFOLIO_TEST_DB.TEST_SCHEMA.DUMMY_USERS").collect.map { row =>
          TestSnowflakeUser(
            id = row.getLong(0).toString,
            userName = row.getString(1),
            email = row.getString(2),
            signupDate = row.getDate(3).toLocalDate
          )
        }.toList
      }
    }
  }

}


object SnowflakeProvider {
  def apply(snowflakeConfig: SnowflakeConfig)(implicit ec: ExecutionContext): SnowflakeProvider = {
    val config: Map[String, String] = Map(
      "URL" -> snowflakeConfig.url,
      "USER" -> snowflakeConfig.user,
      "ROLE" -> snowflakeConfig.role,
      "WAREHOUSE" -> snowflakeConfig.warehouse,
      "PRIVATEKEY" -> snowflakeConfig.privateKey
    )
    new SnowflakeProvider(config)
  }
}
