package com.simonmarkets.regbi.api.request

import com.simonmarkets.regbi.domain.{HouseholdSortBy, SortOrder}
import com.simonmarkets.regbi.domain.OpenApiDefinitions.{Limit, Offset, SearchPattern}
import io.simon.openapi.annotation.Field.Ref

case class GetHouseholdListRequest(
    filterByConcentration: Option[List[String]],

    filterByLNW: Option[List[String]],

    @Ref(HouseholdSortBy.Ref)
    sortBy: Option[HouseholdSortBy],

    @Ref(SortOrder.Ref)
    sortOrder: Option[SortOrder],

    @Ref(SearchPattern)
    pattern: Option[String],

    @Ref(Offset)
    offset: Option[Int],

    @Ref(Limit)
    limit: Option[Int],
)
