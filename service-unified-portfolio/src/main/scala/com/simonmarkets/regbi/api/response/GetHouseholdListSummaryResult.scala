package com.simonmarkets.regbi.api.response

import com.simonmarkets.unifiedportfolio.domain.data.{LPLAccountExtensions, Money, ProductType}

case class GetHouseholdListSummaryResult(
    id: String,
    name: String,
    lnw: Int,
    siConcentrationPercentage: Float,
    annualExpenses: Option[Int],
    availableLCF: Option[Int],
    obligations: Option[Int],
    profiles: Seq[DecryptedProfile]
)

case class DecryptedProfile(
    id: String,
    experience: Int,
    age: Int,
    name: String,
    productTypes: List[ProductType],
    accounts: Seq[DecryptedAccount]
)

case class DecryptedAccount(
    id: String,
    name: String,
    investmentObjective: String,
    documents: Seq[String],
    totalValue: Option[Money],
    partnerExtensionsLPL: Option[LPLAccountExtensions],
    liquidNetWorthMin: Option[Money],
    liquidNetWorthMax: Option[Money],
)
