package com.simonmarkets.regbi.repository

import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.regbi.domain.{HouseholdSortBy, SortOrder}
import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, ExperienceProductType, HoldingType, LegalEntityType, OwnerType}
import com.simonmarkets.unifiedportfolio.domain.data.{Authorization, HouseholdDetails, LPLAccountExtensions, Money, Owner, OwnerDetails, ProductType, RecordTimestamps}
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros._
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.bson.{BsonArray, Document}
import org.mongodb.scala.model.Aggregates._
import org.mongodb.scala.model.Filters.{and, equal, expr, gte, in, lte, or}
import org.mongodb.scala.model.Projections.{computed, fields, include}
import org.mongodb.scala.model.{Accumulators, Aggregates, Facet, Variable}
import OwnersPresentationRepositoryMongo.{HouseholdAggregation, ShallowHousehold, ShallowHouseholdDetails}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.resteasy.utils.SimonCodecs
import com.simonmarkets.unifiedportfolio.domain.enums.lpl.{LPLAccountPrimaryClassification, LPLAccountRType, LPLAccountSecondaryClassification}
import com.simonmarkets.utils.data.mongo.SimonMongoRepository
import io.simon.encryption.v2.model.EncryptedData

import scala.concurrent.{ExecutionContext, Future}

trait OwnersPresentationRepository {

  def getHouseholdById(houseHoldId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Option[ShallowHouseholdDetails]]

  def getHouseholdList(
      ids: Seq[String],
      availableAccessKeys: Set[String],
      sortOrder: SortOrder,
      sortBy: HouseholdSortBy,
      filterByLNW: Option[List[String]],
      filterByConcentration: Option[List[String]],
      offset: Option[Int],
      limit: Option[Int]
  )(implicit traceId: TraceId): Future[Seq[HouseholdAggregation]]

  def getHouseholdNames(availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Seq[ShallowHousehold]]
}

class OwnersPresentationRepositoryMongo(implicit dbCtx: DatabaseContext, ec: ExecutionContext) extends SimonMongoRepository[Owner] with OwnersPresentationRepository {

  private lazy val collection = dbCtx.getCollection[Owner](OwnersPresentationRepositoryMongo.collectionName).withCodecRegistry(OwnersPresentationRepositoryMongo.codecRegistry)

  override def getHouseholdById(houseHoldId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Option[ShallowHouseholdDetails]] = {
    collection
      .find[ShallowHouseholdDetails](
        and(
          equal("id", houseHoldId),
          equal("ownerType", OwnerType.Household),
          accessKeysCondition(availableAccessKeys)
        )
      )
      .comment(traceId.traceId)
      .headOption
  }

  override def getHouseholdList(
      ids: Seq[String] = Seq.empty,
      availableAccessKeys: Set[String],
      sortOrder: SortOrder,
      sortBy: HouseholdSortBy,
      filterByLNW: Option[List[String]],
      filterByConcentration: Option[List[String]],
      offset: Option[Int],
      limit: Option[Int]
  )(implicit traceId: TraceId): Future[Seq[HouseholdAggregation]] = {
    val order = sortOrder.intValue
    val sortCondition = sortBy match {
      case HouseholdSortBy.Id => Seq(sort(equal("id", order)))
      case HouseholdSortBy.LNW => Seq(sort(equal("lnw", order)))
      case HouseholdSortBy.SiConcentration => Seq(sort(equal("siConcentrationPercentage", order)))
      case _ => Seq.empty
    }

    val lnwFilter = filterByLNW match {
      case Some(filters) => {
        filters.flatMap { filter =>
          val currentFilter = filter.split("-").map(_.toFloat)
          if (currentFilter.length == 2) {
            Seq(and(gte("lnw", currentFilter.min), lte("lnw", currentFilter.max)))
          } else {
            Seq(and(gte("lnw", currentFilter.min)))
          }
        }
      }
      case _ => Seq.empty
    }

    val concentrationFilter = filterByConcentration match {
      case Some(filters) => {
        filters.flatMap { filter =>
          val currentFilter = filter.split("-").map(_.toFloat)
          if (currentFilter.length == 2) {
            Seq(and(gte("siConcentrationPercentage", currentFilter.min), lte("siConcentrationPercentage", currentFilter.max)))
          } else {
            Seq(and(gte("siConcentrationPercentage", currentFilter.min)))
          }
        }
      }
      case _ => Seq.empty
    }

    val accountsLookUp = lookup(
      from = "owners",
      let = Seq(Variable("legalEntityId", "$id")),
      pipeline = Seq(
        `match`(
          and(
            equal("ownerType", OwnerType.Account),
            expr(Document("$in" -> BsonArray("$$legalEntityId", "$parentIds")))
          )
        ),
        project(
          fields(
            computed("investmentObjective", "$details.investmentObjective"),
            include("id", "name"),
            computed("documents", "$details.documents"),
            computed("totalValue", "$details.totalValue"),
            computed("partnerExtensionsLPL", "$details.partnerExtensionsLPL"),
            computed("liquidNetWorthMin", "$details.liquidNetWorthMin"),
            computed("liquidNetWorthMax", "$details.liquidNetWorthMax")
          )
        )
      ),
      as = "accounts"
    )

    val SiConcentrationLookUp = lookup(
      from = "holdings",
      let = Seq(Variable("householdId", "$id"), Variable("householdLNW", "$lnw")),
      pipeline = Seq(
        `match`(
          and(
            equal("holdingType", HoldingType.StructuredInvestment),
            expr(Document("$in" -> BsonArray("$$householdId", "$ownerAncestorIds")))
          )
        ),
        group(
          "$householdId",
          Accumulators.sum("totalCommitmentValue", "$totalCommitment.amount")
        ),
        project(
          fields(
            Document("siConcentration" ->
              Document("$multiply" -> BsonArray(
                Document("$cond" -> Document(
                  "if" -> Document("$eq" -> BsonArray("$$householdLNW", 0)),
                  "then" -> 0,
                  "else" -> Document("$divide" -> BsonArray("$totalCommitmentValue", "$$householdLNW"))
                )),
                100
              ))
            )
          )
        )),
      as = "siConcentration"
    )

    val profilesLookUp = lookup(
      from = "owners",
      let = Seq(Variable("householdId", "$id")),
      pipeline = Seq(
        `match`(
          and(
            equal("ownerType", OwnerType.LegalEntity),
            expr(Document("$in" -> BsonArray("$$householdId", "$parentIds")))
          )
        ),
        project(
          fields(
            include("id", "name"),
            computed("experience", "$details.entity.experience.years"),
            computed("age", "$details.entity.age"),
            computed("productTypes", "$details.entity.productTypes")
          )
        ),
        accountsLookUp
      ),
      as = "profiles"
    )

    val idsFilter = if (ids.isEmpty) Document() else in("id", ids: _*)
    val firstStageFilter = Seq(
      equal("ownerType", OwnerType.Household),
      idsFilter,
      accessKeysCondition(availableAccessKeys)
    )
    val householdFilters = Seq(
      `match`(
        and(firstStageFilter: _*)
      ),
      profilesLookUp,
      project(
        fields(
          computed(
            "lnw",
            // we compute lnw here in order to calculate si concentration on a db level
            // we check here if we have overridden household liquidNetWorth and if not we look for
            // the biggest liquidNetWorthMin across all of the accounts under a household
            // if all above values are None we default it to 0
            Document(
              "$ifNull" -> BsonArray(
                "$details.liquidNetWorth.amount",
                Document(
                  "$ifNull" -> BsonArray(
                    Document(
                      "$max" -> Document(
                        "$map" -> Document(
                          "input" -> Document(
                            "$reduce" -> Document(
                              "input" -> "$profiles",
                              "initialValue" -> BsonArray(),
                              "in" -> Document(
                                "$concatArrays" -> BsonArray("$$value", "$$this.accounts")
                              )
                            )
                          ),
                          "as" -> "account",
                          "in" -> "$$account.liquidNetWorthMin.amount"
                        )
                      )
                    ),
                    0
                  )
                )
              )
            )
          ),
          include("id"),
          include("name"),
          include("profiles"),
          computed("annualExpenses", "$details.annualExpenses.amount"),
          computed("availableLCF", "$details.availableLCF.amount"),
          computed("obligations", "$details.obligations.amount"),
        )
      )
    )
    val postLookUpFilters = {
      val lnwFilters = if (lnwFilter.nonEmpty) Seq(or(lnwFilter: _*)) else Seq.empty
      val concentrationFilters = if (concentrationFilter.nonEmpty) Seq(or(concentrationFilter: _*)) else Seq.empty
      val allFilters = lnwFilters ++ concentrationFilters
      if (allFilters.isEmpty) Seq.empty
      else Seq(`match`(and(allFilters: _*)))
    }

    val resultPipeline = Facet("results",
      householdFilters ++ Seq(
        SiConcentrationLookUp,
        project(
          fields(
          include("profiles"),
          include("id"),
          include("name"),
          include("lnw"),
          computed("annualExpenses", "$details.annualExpenses.amount"),
          computed("availableLCF", "$details.availableLCF.amount"),
          computed("obligations", "$details.obligations.amount"),
          computed("siConcentrationPercentage",
            Document("$ifNull" ->
              BsonArray(
                Document("$arrayElemAt" ->
                  BsonArray("$siConcentration.siConcentration", 0)
                ), 0
              )
            )
          ),
        ))
      ) ++ postLookUpFilters ++ sortCondition ++ offset.flatMap(offs => if (offs > 0) Some(Aggregates.skip(offs)) else None).toSeq ++ limit.map(Aggregates.limit).toSeq: _*)

    val siConcentrationLookUpCondition = if (concentrationFilter.isEmpty) Seq.empty else Seq(SiConcentrationLookUp)
    val countPipeline = Facet("totalCount", householdFilters ++ siConcentrationLookUpCondition ++ Seq(project(
      fields(
        include("lnw"),
        computed("siConcentrationPercentage",
          Document("$ifNull" ->
            BsonArray(
              Document("$arrayElemAt" ->
                BsonArray("$siConcentration.siConcentration", 0)
              ), 0
            )
          )
        ),
      ))) ++ postLookUpFilters ++ Seq(count("numberOfItems")): _*)

    collection.aggregate[HouseholdAggregation](Seq(facet(resultPipeline, countPipeline))).comment(traceId.traceId).toFuture
    }

  override def getHouseholdNames(availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Seq[ShallowHousehold]] = {
    collection.find[ShallowHousehold](and(equal("ownerType", OwnerType.Household), accessKeysCondition(availableAccessKeys)))
      .projection(fields(include("id"), include("name")))
      .comment(traceId.traceId)
      .toFuture
  }

  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)
}

object OwnersPresentationRepositoryMongo {

  val collectionName = "owners"

  case class Account(
      id: String,
      name: EncryptedData,
      investmentObjective: String,
      documents: Seq[String],
      totalValue: Option[Money],
      partnerExtensionsLPL: Option[LPLAccountExtensions],
      liquidNetWorthMin: Option[Money],
      liquidNetWorthMax: Option[Money],
  )

  case class Profile(
      id: String,
      experience: Option[Int] = Some(0),
      age: Int,
      name: EncryptedData,
      productTypes: List[ProductType],
      accounts: Seq[Account]
  )
  case class HouseholdSummary(
      id: String,
      name: EncryptedData,
      lnw: Int,
      siConcentrationPercentage: Float,
      annualExpenses: Option[Int],
      availableLCF: Option[Int],
      obligations: Option[Int],
      profiles: Seq[Profile]
  )

  case class ItemCount(
      numberOfItems: Long
  )

  case class HouseholdAggregation(
      results: Seq[HouseholdSummary],
      totalCount: List[ItemCount]
  )

  case class ShallowHousehold(id: String, name: EncryptedData)
  case class DecryptedShallowHousehold(id: String, name: String)

  case class ShallowHouseholdDetails(id: String, details: HouseholdDetails)

  val codecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[HouseholdSummary],
      createCodecProviderIgnoreNone[Money],
      createCodecProviderIgnoreNone[Owner],
      createCodecProviderIgnoreNone[Authorization],
      createCodecProviderIgnoreNone[RecordTimestamps],
      createCodecProviderIgnoreNone[DecryptedShallowHousehold],
      createCodecProviderIgnoreNone[Profile],
      createCodecProviderIgnoreNone[Account],
      createCodecProviderIgnoreNone[EncryptedData],
      createCodecProviderIgnoreNone[ProductType],
      createCodecProviderIgnoreNone[ItemCount],
      createCodecProviderIgnoreNone[HouseholdAggregation],
      createCodecProviderIgnoreNone[ShallowHousehold],
      createCodecProviderIgnoreNone[ShallowHouseholdDetails],
      createCodecProviderIgnoreNone[LPLAccountExtensions],
      EnumEntryCodecProvider[ExperienceProductType],
      EnumEntryCodecProvider[LegalEntityType],
      EnumEntryCodecProvider[HoldingType],
      EnumEntryCodecProvider[OwnerType],
      EnumEntryCodecProvider[Currency],
      EnumEntryCodecProvider[LPLAccountPrimaryClassification],
      EnumEntryCodecProvider[LPLAccountSecondaryClassification],
      EnumEntryCodecProvider[LPLAccountRType],
      classOf[HouseholdDetails],
      SimonCodecs.SimonIdProvider,
      classOf[OwnerDetails]
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
