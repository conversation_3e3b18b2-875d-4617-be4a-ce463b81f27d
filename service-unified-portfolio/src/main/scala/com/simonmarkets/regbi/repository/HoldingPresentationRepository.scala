package com.simonmarkets.regbi.repository

import com.simonmarkets.unifiedportfolio.domain.enums.{Currency, HoldingType, OwnerType}
import com.simonmarkets.unifiedportfolio.domain.data.{ExternalId, Holding, Money, ProductId}
import com.simonmarkets.utils.data.mongo.context.DatabaseContext
import org.bson.codecs.configuration.CodecRegistries.{fromProviders, fromRegistries}
import org.bson.codecs.configuration.CodecRegistry
import org.mongodb.scala.MongoClient.DEFAULT_CODEC_REGISTRY
import org.mongodb.scala.bson.codecs.Macros.createCodecProviderIgnoreNone
import org.mongodb.scala.bson.conversions.Bson
import org.mongodb.scala.model.Filters.{and, equal, in}
import org.mongodb.scala.model.Projections.{fields, include}
import com.simonmarkets.logging.TraceId
import com.simonmarkets.mongodb.codec.EnumEntryCodecProvider
import com.simonmarkets.regbi.repository.HoldingPresentationRepositoryMongo.ShallowHolding
import com.simonmarkets.resteasy.utils.SimonCodecs
import com.simonmarkets.utils.data.mongo.SimonMongoRepository

import scala.concurrent.{ExecutionContext, Future}

trait HoldingPresentationRepository {
  def getHoldingsByHousehold(householdId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Seq[ShallowHolding]]
}

class HoldingPresentationRepositoryMongo(implicit dbCtx: DatabaseContext, ec: ExecutionContext) extends SimonMongoRepository[Holding] with HoldingPresentationRepository {

  private lazy val collection = dbCtx.getCollection[Holding](HoldingPresentationRepositoryMongo.collectionName).withCodecRegistry(HoldingPresentationRepositoryMongo.codecRegistry)

  override def getHoldingsByHousehold(householdId: String, availableAccessKeys: Set[String])(implicit traceId: TraceId): Future[Seq[ShallowHolding]] = {
    collection.find[ShallowHolding](
      and(
        equal("holdingType", HoldingType.StructuredInvestment),
        in("ownerAncestorIds", householdId),
        accessKeysCondition(availableAccessKeys))
    )
      .projection(
        fields(
          include("id"),
          include("totalCommitment"),
          include("productId"))
      )
      .comment(traceId.traceId)
      .toFuture
  }

  private def accessKeysCondition(availableAccessKeys: Set[String]): Bson =
    in("acceptedAccessKeys", availableAccessKeys.toSeq: _*)
}

object HoldingPresentationRepositoryMongo {

  val collectionName = "holdings"

  case class ShallowHolding(
      id: String,
      totalCommitment: Money,
      productId: ProductId
  )

  val codecRegistry: CodecRegistry = fromRegistries(
    fromProviders(
      createCodecProviderIgnoreNone[ProductId],
      createCodecProviderIgnoreNone[ExternalId],
      createCodecProviderIgnoreNone[Money],
      createCodecProviderIgnoreNone[ProductId],
      createCodecProviderIgnoreNone[ShallowHolding],
      EnumEntryCodecProvider[Currency],
      EnumEntryCodecProvider[HoldingType],
      EnumEntryCodecProvider[OwnerType],
      SimonCodecs.SimonIdProvider,
    ),
    DEFAULT_CODEC_REGISTRY
  )
}
