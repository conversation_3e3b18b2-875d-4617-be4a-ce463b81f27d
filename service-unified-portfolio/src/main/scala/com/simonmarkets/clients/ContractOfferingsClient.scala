package com.simonmarkets.clients

import akka.stream.Materializer
import com.simonmarkets.clients.ContractOfferingsClient.OfferingsResult
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}

import scala.concurrent.{ExecutionContext, Future}

trait ContractOfferingsClient {
  def getIssuerNames(assetIds: Seq[String])(implicit traceId: TraceId): Future[OfferingsResult]
}

object ContractOfferingsClient extends TraceLogging {

  case class OfferingsResult(
    results: List[Offering]
  )

  case class Offering(
    id: String,
    params: OfferingParams,
  )
  case class OfferingParams(
      issuerShortName: String
  )

  def apply(httpClient: FutureHttpClient, basePath: String)
    (implicit traceId: TraceId, ec: ExecutionContext, mat: Materializer): ContractOfferingsClient = {

    log.info("Creating HttpContractOfferingsClient")
    new HttpContractOfferingsClient(httpClient, basePath)
  }
}
