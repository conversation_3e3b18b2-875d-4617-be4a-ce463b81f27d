package com.simonmarkets.clients

import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ContentType, HttpEntity, StatusCodes, Uri}
import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.circe.JsonCodecs
import com.simonmarkets.clients.HttpAddeparClient.{createAddeparQueryRequest, mapErrorType, portfolioQueryResponseDecoder}
import com.simonmarkets.http.FutureHttpClient.RequestTag
import com.simonmarkets.http.{FutureHttpClient, HttpEncoder, HttpError}
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.AddeparAttribute.{AssetClass, FinancialAccount, OriginalName, Security, ShortName, Ticker, Value}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.QueryType.PortfolioQuery
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.enums.{AddeparAttribute, QueryType}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.{AddeparAccessToken, AddeparAttributeArguments, AddeparFirm, PortfolioQueryAttribute, PortfolioQueryAttributes, PortfolioQueryData}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.requests.PortfolioQueryRequest
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses.{AddeparEntitiesResponse, PortfolioQueryDataChild, PortfolioQueryDataColumns, PortfolioQueryResponse, PortfolioQueryResponseAttributes, PortfolioQueryResponseData, PortfolioQueryTotalSummary}
import com.simonmarkets.unifiedportfolio.domain.requests.{GetHoldingRequest, OwnerListRequest}
import io.circe.generic.semiauto.deriveDecoder
import io.circe.{Decoder, Encoder, Json}

import java.time.LocalDate

import scala.concurrent.{ExecutionContext, Future}

case class HttpAddeparClient(httpClient: FutureHttpClient, basePath: String)
  (implicit ec: ExecutionContext, mat: Materializer)
  extends AddeparClient with TraceLogging with JsonCodecs {

  private implicit val requestTag: RequestTag = FutureHttpClient.Tag("AddeparClient")

  override def getEntitiesByType(request: OwnerListRequest)
    (implicit user: UserACL, traceId: TraceId, addeparToken: AddeparAccessToken,
        addeparFirm: AddeparFirm): Future[AddeparEntitiesResponse] = {
    val offset = request.offset.getOrElse(0)
    val limit = request.limit.getOrElse(10)

    val headers = List(
      RawHeader("Addepar-Firm", addeparFirm.firmId),
      RawHeader("Authorization", s"Bearer ${addeparToken.token}")
    )
    val uri = Uri(s"$basePath/api/v1/entities")
      .withQuery(
        Query(
            "fields[entities]" -> OriginalName.value,
            "page[after]" -> offset.toString,
            "page[limit]" -> limit.toString,
          //TODO - change this to match based on the OwnerType when we expand support for other types
            "filter[entity_types]" -> FinancialAccount.value
        )
      )

    httpClient.get[AddeparEntitiesResponse](uri, requestHeaders = headers)
      .recoverWith(mapErrorType)
  }

  override def portfolioQueryById(request: GetHoldingRequest)
    (implicit userACL: UserACL, traceId: TraceId, addeparToken: AddeparAccessToken,
        addeparFirm: AddeparFirm): Future[PortfolioQueryResponse] = {
    //We need this in order to correctly set the Content-Type header but, we can't manually set it due to
    //limitations from the httpClient library
    import HttpAddeparClient.httpEncoder

    log.info(s"Getting all holdings for entity ${request.id}")
    val headers = List(
      RawHeader("Addepar-Firm", addeparFirm.firmId),
      RawHeader("Authorization", s"Bearer ${addeparToken.token}"),
    )
    val uri = Uri(s"$basePath/api/v1/portfolio/query")
    val addeparRequest = createAddeparQueryRequest(request)

    httpClient.post[PortfolioQueryRequest, PortfolioQueryResponse](
      uri = uri,
      data = addeparRequest,
      requestHeaders = headers)
      .recoverWith(mapErrorType)
  }
}


object HttpAddeparClient extends TraceLogging {
  private def createAddeparQueryRequest(holdingsRequest: GetHoldingRequest): PortfolioQueryRequest = {
    /** These columns will need to match the columns in the response model: [[PortfolioQueryTotalSummary.columns]]*/
    val columns = Seq(
      PortfolioQueryAttribute(Ticker, None),
      PortfolioQueryAttribute(ShortName, None),
      PortfolioQueryAttribute(Value, Some(AddeparAttributeArguments.default)),
    )
    val groupings = Seq(
      PortfolioQueryAttribute(AssetClass, None),
      PortfolioQueryAttribute(Security, None),
    )

    val queryAttributes = PortfolioQueryAttributes(
      columns,
      groupings,
      portfolioType = "Entity",
      portfolioId = Seq(holdingsRequest.id.toInt),
      startDate = LocalDate.of(1900, 1, 1),
      endDate = LocalDate.now
    )
    PortfolioQueryRequest(
      data = PortfolioQueryData(
        `type` = PortfolioQuery,
        attributes = queryAttributes
      )
    )
  }

  implicit val addeparAttributeArgumentsEncoder: Encoder[AddeparAttributeArguments] =
    Encoder.forProduct5("valuation_adjustment", "negative_value_handling", "time_point", "accrued", "currency")(
      attr => (
        attr.valuationAdjustmentType,
        attr.negativeValueHandling,
        attr.timePoint,
        attr.accrued,
        attr.currency
      )
    )

  implicit val addeparAttributeEncoder: Encoder[AddeparAttribute] = Encoder.encodeString.contramap(_.value)
  implicit lazy val addeparAttributeDecoder: Decoder[AddeparAttribute] = Decoder.decodeString.map(AddeparAttribute.apply)

  implicit val portfolioQueryAttributeEncoder: Encoder[PortfolioQueryAttribute] =
    Encoder.instance(attr => Json.obj("key" -> Json.fromString(attr.key.value)))

  implicit val queryTypeEncoder: Encoder[QueryType] = Encoder.forProduct1("type")(_.value)
  implicit lazy val queryTypeDecoder: Decoder[QueryType] = Decoder.decodeString.map(QueryType.apply)

  implicit val portfolioQueryAttributesEncoder: Encoder[PortfolioQueryAttributes] =
    Encoder.forProduct6(
      "columns",
      "groupings",
      "portfolio_type",
      "portfolio_id",
      "start_date",
      "end_date"
    )(attr => (
      attr.columns,
      attr.groupings,
      attr.portfolioType,
      attr.portfolioId,
      attr.startDate,
      attr.endDate
    ))

  implicit val portfolioQueryDataEncoder: Encoder[PortfolioQueryData] = Encoder.forProduct2("type",
    "attributes")(data => (data.`type`.value, data.attributes))

  implicit val portfolioQueryRequestEncoder: Encoder[PortfolioQueryRequest] = Encoder.forProduct1("data")(_.data)

  implicit lazy val portfolioQueryDataColumnsDecoder: Decoder[PortfolioQueryDataColumns] = Decoder.forProduct4(
    "ticker_symbol",
    "value",
    "short_name",
    "cusip")(PortfolioQueryDataColumns.apply)

  implicit lazy val portfolioQueryDataChildDecoder: Decoder[PortfolioQueryDataChild] = deriveDecoder

  implicit lazy val portfolioQueryTotalSummaryDecoder: Decoder[PortfolioQueryTotalSummary] = Decoder.forProduct3(
    "name",
    "columns",
    "children")(PortfolioQueryTotalSummary.apply)

  implicit lazy val portfolioQueryResponseAttributesDecoder: Decoder[PortfolioQueryResponseAttributes] = Decoder.forProduct1("total")(PortfolioQueryResponseAttributes.apply)

  implicit lazy val portfolioQueryResponseDataDecoder: Decoder[PortfolioQueryResponseData] = Decoder.forProduct1("attributes")(PortfolioQueryResponseData.apply)

  implicit lazy val portfolioQueryResponseDecoder: Decoder[PortfolioQueryResponse] = Decoder.forProduct1("data")(PortfolioQueryResponse.apply)

  import io.circe.syntax._

  implicit val httpEncoder: HttpEncoder[PortfolioQueryRequest] = (data: PortfolioQueryRequest) => {
    val jsonString = data.asJson.noSpaces
    val contentType = ContentType.parse("application/vnd.api+json").right.get
    HttpEntity(contentType, jsonString.getBytes)
  }

  private def mapErrorType[T](implicit traceId: TraceId): PartialFunction[Throwable, Future[T]] = {
    case e: HttpError if e.status == StatusCodes.Unauthorized =>
      log.error(e.getMessage)
      Future.failed(HttpError.badRequest("Addepar OAuth Error"))

    case e: HttpError if e.status == StatusCodes.BadRequest =>
      log.error(e.getMessage)
      Future.failed(HttpError.badRequest("Unable to retrieve results due to a request error"))

    case e: HttpError if e.status == StatusCodes.NotFound =>
      log.error(e.getMessage)
      Future.failed(HttpError.notFound("Addepar data not found"))

    case e: HttpError =>
      log.error(e.getMessage)
      Future.failed(HttpError.internalServerError("Addepar API Error"))
  }
}
