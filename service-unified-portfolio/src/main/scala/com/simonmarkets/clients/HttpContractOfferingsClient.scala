package com.simonmarkets.clients

import akka.http.scaladsl.model.Uri
import akka.http.scaladsl.model.Uri.Query
import akka.stream.Materializer
import com.simonmarkets.clients.ContractOfferingsClient.OfferingsResult
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import io.circe.generic.auto._

import scala.concurrent.{ExecutionContext, Future}

class HttpContractOfferingsClient(httpClient: FutureHttpClient, basePath: String)(implicit ec: ExecutionContext, mat: Materializer)
  extends ContractOfferingsClient with TraceLogging {

  private implicit val requestTag: FutureHttpClient.RequestTag = FutureHttpClient.Tag("ContractOfferings")

  override def getIssuerNames(assetIds: Seq[String])(implicit traceId: TraceId): Future[OfferingsResult] = {
    log.info("getting offerings for", assetIds)
    val ids = assetIds.map(a => "identifier" -> a)
    val uri = Uri(s"$basePath/v1/contract-offerings?includeCanBeOfferedTo=false")
      .withQuery(Query(
        List(
          "representation" -> "internal",
          "action" -> "view",
        ) ++ ids: _*))
    httpClient.get[OfferingsResult](uri)
  }

}
