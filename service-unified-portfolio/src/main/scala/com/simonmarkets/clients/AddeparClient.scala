package com.simonmarkets.clients

import akka.stream.Materializer
import com.goldmansachs.marquee.pipg.UserACL
import com.simonmarkets.http.FutureHttpClient
import com.simonmarkets.logging.{TraceId, TraceLogging}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.models.{AddeparAccessToken, AddeparFirm}
import com.simonmarkets.unifiedportfolio.domain.integrations.addepar.responses.{AddeparEntitiesResponse, PortfolioQueryResponse}
import com.simonmarkets.unifiedportfolio.domain.requests.{GetHoldingRequest, OwnerListRequest}

import scala.concurrent.{ExecutionContext, Future}


trait AddeparClient {
  def getEntitiesByType(request: OwnerListRequest)(implicit user: UserACL, traceId: TraceId, addeparToken: AddeparAccessToken, addeparFirm: AddeparFirm): Future[AddeparEntitiesResponse]

  def portfolioQueryById(request: GetHoldingRequest)(implicit userACL: UserACL, traceId: TraceId, addeparToken: AddeparAccessToken, addeparFirm: AddeparFirm): Future[PortfolioQueryResponse]
}

object AddeparClient extends TraceLogging {
  def apply(httpClient: FutureHttpClient, basePath: String)
    (implicit traceId: TraceId, ec: ExecutionContext, mat: Materializer): AddeparClient = {

    log.info("Creating HttpAddeparClient")
    HttpAddeparClient(httpClient, basePath)
  }
}
