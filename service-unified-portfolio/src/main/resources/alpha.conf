info {
  name = "Unified Portfolio Service"
  description = "Serving holdings across all asset types as well as their ownership graph!"
  repository = "TODO"
  module = "service-unified-portfolio"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "TODO"
  used-by = ["Architect and LPL"]
  owner = "<PERSON> Forsyth"
  support-distro = ["TODO"]
  metrics-url = "TODO",
  documentation-url = "TODO"
}

system-routes {
  service-up {
    path = "simon/api/v1/unified-portfolio/uptime"
  }
  service-info {
    path = "simon/api/v1/unified-portfolio/info"
  }
  health-check {
    path = "simon/api/v1/unified-portfolio/healthcheck"
  }
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

system-user-id = "0oaevl54gplnFvyx70h7"

run-mode {
  type = server-mode
  http-server-config {
    port = 443
    interface = "0.0.0.0"
    ssl {
      enabled = true
      keystore {
        type = "JKS"
        file = "/var/cv/creds/simon-vertx-ssl.jks"
        password = "sm:applicationconfig-simon-vertx-ssl-jks-pass"
      }
    }
  }
}

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oaly557tekGOSbQw1d7"
      client-secret = "sm:unified-portfolio-service-client-secret"
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = BearerToken
      }
    }
    proxy {
      address = "internal-alpha-squid-proxy-elb-**********.us-east-1.elb.amazonaws.com"
      port = 3128
    }
  }
  base-url = "https://origin-a.dev.simonmarkets.com/simon/api"
  cache-config {
      enabled = true
      config {
          service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
          signin-region = "us-east-1"
      }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader" # header / bearer
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      keys-url = "sm:applicationconfig-okta-validator"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}

mongo-db {
  owner {
    database = "unifiedPortfolio"
    collection = "owners"
  }
  holding {
    database = "unifiedPortfolio"
    collection = "holdings"
  }
  client {
    app-name = "service-unified-portfolio"
    connection {
      url = "mongodb+srv://cluster0-pvlpe.mongodb.net/unifiedPortfolio?retryWrites=true&w=majority"
      authentication {
        type = "password"
        user = "alpha-unifiedportfolio-user-rw"
        password = "sm:applicationconfig-mongodb-unifiedportfolio-rw"
        database = "admin"
      }
    }
  }
}

icn-encryption {
    endpoint = "https://origin-a.dev.simonmarkets.com/kms/api/v1",
    environment = "development",
    simon-base-url = "https://origin-a.dev.simonmarkets.com",
    hashing-key-json = "sm:applicationkey-hashing"
}

snowflake {
    url = "https://pia34755.us-east-1.snowflakecomputing.com",
    user = "DEV_UNIFIED_PORTFOLIO_USER",
    role = "DEV_UNIFIED_PORTFOLIO_ROLE",
    warehouse = "DEV_UNIFIED_PORTFOLIO_WH",
    private-key = "sm:unified-portfolio-snowflake-pem"
}

addepar {
    base-url = "https://icapital.clientdev.addepar.com"
}
