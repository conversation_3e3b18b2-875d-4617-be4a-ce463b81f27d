<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.simonmarkets.logging.SimonLogbackJsonLayout">
                <appendLineSeparator>true</appendLineSeparator>
                <timestampFormat>yyyy-MM-dd HH:mm:ss,SSS</timestampFormat>
                <timestampFormatTimezoneId>Etc/UTC</timestampFormatTimezoneId>
                <parseApplicationLog>true</parseApplicationLog>
            </layout>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
    </root>
</configuration>