info {
  name = "Unified Portfolio Service"
  description = "Serving holdings across all asset types as well as their ownership graph!"
  repository = "TODO"
  module = "service-unified-portfolio"
  version = "${project.version}"
  revision = "${git.commit.id.abbrev}"
  build-timestamp = "${build.timestamp.millis}"
  product-line = "TODO"
  used-by = ["Architect and LPL"]
  owner = "<PERSON> Forsyth"
  support-distro = ["TODO"]
  metrics-url = "TODO",
  documentation-url = "TODO"
}

system-routes {
  service-up {
    path = "simon/api/v1/unified-portfolio/uptime"
  }
  service-info {
    path = "simon/api/v1/unified-portfolio/info"
  }
  health-check {
    path = "simon/api/v1/unified-portfolio/healthcheck"
  }
}

config.resolvers {
  sm {
    region = "us-east-1"
  }
  file {
    root = "app-config/dev"
  }
}

system-user-id = "0oaevl54gplnFvyx70h7"

run-mode {
  type = server-mode
  http-server-config {
    port = 443
    interface = "0.0.0.0"
    ssl {
      enabled = true
      keystore {
        type = "JKS"
        file = "/var/cv/creds/simon-vertx-ssl.jks"
        password = "sm:applicationconfig-simon-vertx-ssl-jks-pass"
      }
    }
  }
}

acl-client-config {
  http-client {
    auth {
      type = OAuth
      client-id = "0oa2dvyt9chdu995Y0h8"
      client-secret = "sm:unified-portfolio-service-client-secret"
      client-secret-file = ""
      site = "sm:applicationconfig-issuer-uri"
      authorization-path = ""
      token-path = "/v1/token"
      scope = "read_product_data"
      token-type {
        type = BearerToken
      }
      proxy {
        port = 3128
        address = "internal-qa-squid-elb-**********.us-east-1.elb.amazonaws.com"
      }
    }
    proxy {
      port = 3128
      address = "internal-qa-squid-elb-**********.us-east-1.elb.amazonaws.com"
    }
  }
  base-url = "https://origin-dc1.api.qa.simonmarkets.com/simon/api"
  cache-config {
      enabled = false
      config {
          service-endpoint = "https://dynamodb.us-east-1.amazonaws.com/"
          signin-region = "us-east-1"
      }
  }
}

authentication {
  type = "jwt"
  jwt {
    source {
      type = "cookieOrHeader"
      name = "SimSSO:X-Simon-AccessToken"
    }
    validator {
      issuer-url = "sm:applicationconfig-issuer-uri"
      audience = "simonmarkets.com,0oaesrvhgxBiybQcY0h7"
      clock-offset-in-seconds = 0
      proxy-host = "internal-qa-squid-elb-**********.us-east-1.elb.amazonaws.com"
      proxy-port = 3128
    }
  }

  user-info {
    source {
      type = header-source
      name = "x-simon-accesstoken"
    }
  }
}

mongo-db {
  owner {
    database = "unifiedPortfolio"
    collection = "owners"
  }
  holding {
    database = "unifiedPortfolio"
    collection = "holdings"
  }

  client {
    app-name = "service-unified-portfolio"
    connection {
      url = "mongodb+srv://cluster-qa1.1zcf4.mongodb.net/unifiedPortfolio?retryWrites=true&w=majority"
      authentication {
        type = "password"
        user = "qa-unifiedportfolio-user-rw"
        password = "sm:applicationconfig-mongodb-unifiedportfolio-rw"
        database = "admin"
      }
    }
  }
}

icn-encryption {
    endpoint = "https://origin-dc1.api.qa.simonmarkets.com/kms/api/v1",
    environment = "staging",
    simon-base-url = "https://origin-dc1.api.qa.simonmarkets.com",
    hashing-key-json = "sm:applicationkey-hashing"
}

addepar {
    base-url = "https://icapital.clientdev.addepar.com"
}

snowflake {
    url = "https://pia34755.us-east-1.snowflakecomputing.com",
    user = "DEV_UNIFIED_PORTFOLIO_USER",
    role = "DEV_UNIFIED_PORTFOLIO_ROLE",
    warehouse = "DEV_UNIFIED_PORTFOLIO_WH",
    private-key = "sm:unified-portfolio-snowflake-pem"
}

