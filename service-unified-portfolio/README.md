# Unified Portfolio Service
This service has been auto-generated by SIMON CLI using RestEasy Archetype.
This service is set up to run out of the box, but you should look to the checklist below and address the todos.
If you have any issues with the service generation or running the service, reach out to [Developer Tooling](https://teams.microsoft.com/l/channel/19%3Ae1af275b63ed440a9d432fe4c1e987a8%40thread.tacv2/Developer%20Tooling?groupId=16bff440-ca82-4b72-959f-52b48b1b2164&tenantId=0ac51d28-23c3-4b35-a9fb-e1a17aa9eff1).

When creating a new service there are few things you'll need to check and fix. 
You can either search for all TODO values in the code, or reference the checklist below.

- [ ] In the src/main/resources/common.conf, under the `info` block, update the following values (where applicable):
  - [ ] description - Give your service a description that will be displayed in Developer Hub
  - [ ] product-line - Reference the [Product Line Resoruces](https://icapitalnetwork.atlassian.net/wiki/spaces/ENG/pages/2980484064/Helpful+Resources+by+Product+Line)
  - [ ] used-by - Who is going to use this service?
  - [ ] owner - Who is the owner? 
  - [ ] support-distro - What email distribution should be used for support?
  - [ ] metrics-url - Is there a Splunks Dashboard?
  - [ ] documentation-url - Is there a confluence page for this service?
- [ ] Request a new System User for your service from DevOps for each environment, alpha, qa, prod and replace all instances of `REPLACE-ME-CLIENT-ID` and `REPLACE-ME-CLIENT-SECRET`
      Note: If this is a new joiner service, you can ignore QA and Prod conf files
  - [ ] Alpha - Update local.conf, simon-cli.conf, and alpha.conf with the correct credential values
  - [ ] QA - Update qa.conf
  - [ ] Prod - Update prod.conf
- [ ] Request a new Role ARN from DevOps for the `values.{env}.yaml` files in order to deploy your EKS service

In order to run our service locally, there are a few steps that need to be done first to set up our local environment.

As part of your onboarding process, you should have set up SIMON CLI, an internal tool used by developers to help run services locally.
To test whether SIMON is installed or not, try running `simon -v` in the console.
If you receive an error, it may not be installed.
Refer to the [SIMON CLI Documentation](https://icapitalnetwork.atlassian.net/wiki/spaces/ENG/pages/2980432269/Simon+CLI+Documentation) for installation or help.

If you have SIMON CLI installed, ensure it is up to date by running `simon update`.

We use Kong as our API Gateway to route requests to all of our services. 
We'll run this locally with the following command:
```shell
simon kong start
```
This may ask you for your password in order to authenticate to the Alpha AWS Profile `alpha-elevated` for Secret Manager access.

Once Kong is running, you should see a message similar to: `Kong Manager available at http://localhost:8002`
If you navigate to that url, you should be presented with the Kong Gateway and a single service called `AlphaUpstream`.
This service will redirect all calls that do not match an existing route pattern in your local Kong instance to Alpha Kong.

If you are using MongoDb as part of your service, then there should be a configuration that has been generated for you
as part of the `src/main/resources/common.conf` file.
When we are running locally, we'll need a local instance of MongoDb running.
```shell
simon mongo start
```
This will start the `mongodb` container within the `simon-network` docker network and other services can connect to it via `mongodb:27017`

If you wish to sync some collections from Alpha to your local instance of Mongo, you can use the following command
```shell
simon mongo sync
```

Before starting your service, you'll need to ensure that you are currently logged into Alpha AWS in order to substitute
Secret Manger values in your configurations.
```shell
simon login aws -p alpha-elevated
```

Afterwards, we can start our service using two different options. If you wish to debug in IntelliJ, you will need to
create a run configuration.
When creating a Run Configuration, make sure to add extra VM Options with the value 
`-Dconfig.file=service-unified-portfolio/src/main/resources/local.conf` and a MainClass value of `com.simonmarkets.unifiedportfolio.UnifiedPortfolioApp`

SIMON CLI is capable of running your service in a dockerized environment that tries to reflect as close as possible to
an actual deployed environment.

To run your service with SIMON CLI, you'll want to use the following command and select your service from the repository root.
You can also let SIMON CLI build your service with the `--build` flag.
```shell
simon start --build
✔ Loading local projects: Done
Search: █
 Select a Service:
  > service-unified-portfolio [eks]
```

When starting this service for the first time, you will be asked to select which local config to use.
Ensure you are selecting:
```shell
service-unified-portfolio/src/main/resources/simon-cli.conf
```

You'll also be asked to select the Main Class. 
Ensure you are selecting:
```shell
com.simonmarkets.unifiedportfolio.UnifiedPortfolioApp
```

Once your service has deployed, it should have uploaded your openapi spec to your local Kong instance.
You can validate this by using the following command and selecting `local`
```shell
simon kong find
Select an environment:
 > local
✔ local env has been selected
✔ loading local Kong data:  Done
Search: █
 Select a Kong entity to get the URL:
  > service-unified-portfolio [Service]
    AlphaUpstream [Service]
```

We can then make a request to our service via Postman.

In order to hit our service with Postman, you'll need to set up the Postman collections found in [Confluence](https://icapitalnetwork.atlassian.net/wiki/spaces/PI/pages/2766536891/SIMON+API+Authentication)

Once you've set up your environment, you should select the `local` environment.

We can then make a new request for our service:
```shell
GET {{baseUrl}}/v1/unified-portfolio/owners/info
```

If everything is successful, you should get a response that is the output of the `info` block that you fixed in
`service-unified-portfolio/src/main/resources/common.conf`