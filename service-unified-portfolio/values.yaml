serviceAccount:
  created: true
  name: "service-unified-portfolio"
  iamARN: "TODO"

service:
  name: service-unified-portfolio
  scheme: https
  port: 443
  cpu: "0.5"
  memory: "1G"
  healthCheck:
    path: /simon/api/v1/unified-portfolio/healthcheck
  initialDelay: 30
  interval: 5
  timeout: 15
  failureThreshold: 3

loadbalancer:
  connectionIdleTimeout: 300

deploy:
  replicas: 2

proxyEnabled: true

apmEnabled: true