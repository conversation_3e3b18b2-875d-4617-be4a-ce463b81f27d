<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.simonmarkets.unified-portfolio</groupId>
  <artifactId>unified-portfolio</artifactId>
  <version>sandbox-SNAPSHOT</version>
  <packaging>pom</packaging>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven-resources-plugin.version>3.2.0</maven-resources-plugin.version>
    <maven-shade-plugin.version>3.2.4</maven-shade-plugin.version>
    <snowflake.snowpark.version>1.15.0</snowflake.snowpark.version>
    <scala-maven-plugin.version>3.2.2</scala-maven-plugin.version>
    <scalatest-maven-plugin.version>2.0.2</scalatest-maven-plugin.version>
    <thirdparty-bom.version>bill-of-material-68_0_0-1815141766</thirdparty-bom.version>
    <circe-generic-extras.version>0.14.4</circe-generic-extras.version>
    <simon.openapi-generator.version>lib-openapi-gen-86_0_0-1284684753</simon.openapi-generator.version>
    <simon.networks.version>networks-192_0_0-1761230494</simon.networks.version>
    <simon.utils.version>utils-1_0_1-1797861871</simon.utils.version>
    <simon.resteasy.version>resteasy-1_0_0-1688181303</simon.resteasy.version>
    <scala.version.short>2.12</scala.version.short>
    <scala.version>2.12.13</scala.version>
    <mockito.version>3.12.4</mockito.version>
    <scala-silencer-plugin.version>1.7.14</scala-silencer-plugin.version>
    <openapi.resourcePath>${project.basedir}/src/main/resources/documentation/</openapi.resourcePath>
    <openapi.template>${openapi.resourcePath}template.yaml</openapi.template>
    <openapi.genOkteto>true</openapi.genOkteto>
    <openapi.output>${project.build.directory}/</openapi.output>
    <timestamp>${maven.build.timestamp}</timestamp>
    <maven.build.timestamp.format>yyyy-MM-dd'T'HH:mm:ss.SSSZ</maven.build.timestamp.format>
  </properties>
  <modules>
    <module>service-unified-portfolio</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.circe</groupId>
        <artifactId>circe-generic-extras_${scala.version.short}</artifactId>
        <version>${circe-generic-extras.version}</version>
      </dependency>
      <dependency>
        <groupId>net.snowflake</groupId>
        <artifactId>snowflake-jdbc</artifactId>
        <version>3.22.0</version>
      </dependency>
      <dependency>
        <groupId>com.snowflake</groupId>
        <artifactId>snowpark</artifactId>
        <version>${snowflake.snowpark.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>third-party-bill-of-material</artifactId>
        <version>${thirdparty-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.resteasy</groupId>
        <artifactId>lib-resteasy-core</artifactId>
        <version>${simon.resteasy.version}</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.resteasy</groupId>
        <artifactId>lib-resteasy-mongodb</artifactId>
        <!-- Fixed version since library still doesn't exist on current resteasy version. Use standard version after updating -->
        <version>${simon.resteasy.version}</version>
      </dependency>
      <dependency>
        <groupId>io.simon</groupId>
        <artifactId>lib-openapi-generator</artifactId>
        <version>${simon.openapi-generator.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-useracl-directive-resteasy</artifactId>
        <version>${simon.networks.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>casbah_${scala.version.short}</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-acl-client-resteasy</artifactId>
        <version>${simon.networks.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-common</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.goldmansachs.marquee</groupId>
        <artifactId>lib-encryption-simon</artifactId>
        <version>${simon.utils.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>casbah_${scala.version.short}</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.s3</groupId>
        <artifactId>lib-s3-documents</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>org.scala-lang.modules</groupId>
        <artifactId>scala-parser-combinators_${scala.version.short}</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-utils-serviceinfo</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-logging-core</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-mongodb</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.utils</groupId>
        <artifactId>lib-data-mongo</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-http-client</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ghik</groupId>
        <artifactId>silencer-lib_${scala.version}</artifactId>
        <version>${scala-silencer-plugin.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.utils</groupId>
        <artifactId>lib-aws-lambda-client</artifactId>
        <version>${simon.utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.goldmansachs.marquee</groupId>
        <artifactId>simon-entitlements</artifactId>
        <version>${simon.networks.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>casbah_${scala.version.short}</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.dbmigrations</groupId>
        <artifactId>lib-db-migration-mongo</artifactId>
        <version>db-migrations-1_0_0-1224251591</version>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-pgp-encryption</artifactId>
        <version>${simon.utils.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-http-client</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-config</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <!-- Test -->
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets.utils</groupId>
        <artifactId>scala-mongo-embedded-test</artifactId>
        <version>${simon.utils.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.simonmarkets</groupId>
        <artifactId>lib-users-test</artifactId>
        <version>${simon.networks.version}</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>com.simonmarkets</groupId>
            <artifactId>lib-users</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.goldmansachs.marquee</groupId>
        <artifactId>lib-encryption-simon</artifactId>
        <version>${simon.utils.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.mongodb</groupId>
            <artifactId>casbah_${scala.version.short}</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <!-- WebIdentityTokenCredentialsProvider needs this -->
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-sts</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.3.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.scala-lang</groupId>
      <artifactId>scala-library</artifactId>
      <version>${scala.version}</version>
    </dependency>
    <dependency>
      <groupId>org.scalatest</groupId>
      <artifactId>scalatest_${scala.version.short}</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.8.1</version>
          <configuration>
            <source>11</source>
            <target>11</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>net.alchim31.maven</groupId>
          <artifactId>scala-maven-plugin</artifactId>
          <version>${scala-maven-plugin.version}</version>
          <executions>
            <execution>
              <goals>
                <goal>compile</goal>
                <goal>testCompile</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <compilerPlugins>
              <compilerPlugin>
                <groupId>com.github.ghik</groupId>
                <artifactId>silencer-plugin_${scala.version}</artifactId>
                <version>${scala-silencer-plugin.version}</version>
              </compilerPlugin>
            </compilerPlugins>
            <!-- https://tpolecat.github.io/2017/04/25/scalac-flags.html -->
            <args>
              <arg>-deprecation</arg>
              <arg>-feature</arg>
              <arg>-language:experimental.macros</arg>
              <arg>-language:higherKinds</arg>
              <arg>-unchecked</arg>
              <arg>-Xfatal-warnings</arg>
              <arg>-Xlint:adapted-args</arg>
              <arg>-Xlint:by-name-right-associative</arg>
              <arg>-Xlint:constant</arg>
              <arg>-Xlint:delayedinit-select</arg>
              <arg>-Xlint:doc-detached</arg>
              <arg>-Xlint:inaccessible</arg>
              <arg>-Xlint:infer-any</arg>
              <arg>-Xlint:missing-interpolator</arg>
              <arg>-Xlint:nullary-override</arg>
              <arg>-Xlint:nullary-unit</arg>
              <arg>-Xlint:option-implicit</arg>
              <arg>-Xlint:package-object-classes</arg>
              <arg>-Xlint:poly-implicit-overload</arg>
              <arg>-Xlint:private-shadow</arg>
              <arg>-Xlint:stars-align</arg>
              <arg>-Xlint:type-parameter-shadow</arg>
              <arg>-Xlint:unsound-match</arg>
              <arg>-Yno-adapted-args</arg>
              <arg>-Ypartial-unification</arg>
              <arg>-Ywarn-dead-code</arg>
              <arg>-Ywarn-extra-implicit</arg>
              <arg>-Ywarn-inaccessible</arg>
              <arg>-Ywarn-infer-any</arg>
              <arg>-Ywarn-nullary-override</arg>
              <arg>-Ywarn-nullary-unit</arg>
              <arg>-Ywarn-macros:after</arg>
              <arg>-Ywarn-unused:implicits</arg>
              <arg>-Ywarn-unused:imports</arg>
              <arg>-Ywarn-unused:locals</arg>
              <arg>-Ywarn-unused:params</arg>
              <arg>-Ywarn-unused:patvars</arg>
              <arg>-Ywarn-unused:privates</arg>
              <arg>-Xmacro-settings:resourcePath=${openapi.resourcePath},templatePath=${openapi.template},genOkteto=${openapi.genOkteto}</arg>
            </args>
            <jvmArgs>
              <!-- Pureconfig requires increased stack size for the compilation -->
              <jvmArg>-Xss12m</jvmArg>
            </jvmArgs>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.20.1</version>
          <configuration>
            <skipTests>true</skipTests>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.scalatest</groupId>
          <artifactId>scalatest-maven-plugin</artifactId>
          <version>${scalatest-maven-plugin.version}</version>
          <configuration>
            <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
            <junitxml>.</junitxml>
            <stdout>G</stdout>
            <tagsToExclude>simon.testsupport.tags.Manual,org.scalatest.tags.Slow</tagsToExclude>
            <parallel>false</parallel>
            <argLine>-Djava.util.logging.config.file=src/test/resources/logging.properties
              -Dlogback.debug=false
              -Dlogback.statusListenerClass=ch.qos.logback.core.status.NopStatusListener
            </argLine>
          </configuration>
          <executions>
            <execution>
              <id>test</id>
              <goals>
                <goal>test</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
          <configuration>
            <propertiesEncoding>ISO-8859-1</propertiesEncoding>
          </configuration>
          <executions>
            <execution>
              <id>copy-resources</id>
              <phase>process-classes</phase>
              <goals>
                <goal>copy-resources</goal>
              </goals>
              <configuration>
                <outputDirectory>${openapi.output}</outputDirectory>
                <resources>
                  <resource>
                    <directory>${openapi.resourcePath}</directory>
                    <filtering>false</filtering>
                  </resource>
                </resources>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>de.smartics.maven.plugin</groupId>
          <artifactId>buildmetadata-maven-plugin</artifactId>
          <version>1.6.1</version>
          <executions>
            <execution>
              <goals>
                <goal>provide-buildmetadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>io.github.git-commit-id</groupId>
          <artifactId>git-commit-id-maven-plugin</artifactId>
          <version>5.0.0</version>
          <executions>
            <execution>
              <goals>
                <goal>revision</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven-shade-plugin.version}</version>
          <configuration>
            <createDependencyReducedPom>false</createDependencyReducedPom>
            <shadedArtifactAttached>true</shadedArtifactAttached>
            <shadedClassifierName>uber</shadedClassifierName>
          </configuration>
          <executions>
            <execution>
              <phase>package</phase>
              <goals>
                <goal>shade</goal>
              </goals>
              <configuration>
                <minimizeJar>true</minimizeJar>
                <filters combine.children="append">
                  <filter>
                    <artifact>org.scala-lang:scala-library</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>com.amazonaws:aws-java-sdk-secretsmanager</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>ch.qos.logback:*</artifact>
                    <includes>**</includes>
                  </filter>
                  <filter>
                    <artifact>ch.qos.logback.contrib:*</artifact>
                    <includes>**</includes>
                  </filter>
                  <filter>
                    <artifact>com.simonmarkets:lib-logging-core</artifact>
                    <includes>**</includes>
                  </filter>
                  <filter>
                    <artifact>com.simonmarkets.logging:*</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>commons-logging:commons-logging</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>org.mongodb:*</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>com.typesafe.akka:akka-stream_${scala.version.short}</artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                  <filter>
                    <artifact>com.typesafe.akka:akka-actor_${scala.version.short}
                    </artifact>
                    <includes>
                      <include>**</include>
                    </includes>
                  </filter>
                </filters>
                <transformers>
                  <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                    <resource>reference.conf</resource>
                  </transformer>
                  <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer"/>
                </transformers>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>com.coderplus.maven.plugins</groupId>
          <artifactId>copy-rename-maven-plugin</artifactId>
          <version>1.0.1</version>
          <executions>
            <execution>
              <id>copy-file</id>
              <phase>package</phase>
              <goals>
                <goal>copy</goal>
              </goals>
              <configuration>
                <sourceFile>target/${project.artifactId}-${project.version}-uber.${project.packaging}</sourceFile>
                <destinationFile>target/${project.artifactId}-uber.${project.packaging}</destinationFile>
              </configuration>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
