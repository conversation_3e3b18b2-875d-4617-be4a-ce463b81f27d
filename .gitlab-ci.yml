include:
  - project: 'simonmarkets/services/sdlc-gitlab-templatization'
    ref: $PIPELINE_TAG
    file:
      - 'templates/simon-services/stages/build_and_test.yml'
      - 'templates/simon-services/stages/pre_deploy.yml'
      - 'templates/simon-services/stages/deploy.yml'
      - 'templates/simon-services/stages/post_deploy.yml'

stages:
  - build/test
  - predeploy_alpha
  - deploy_alpha
  - postdeploy_alpha
  - predeploy_qa
  - deploy_qa
  - postdeploy_qa
  - predeploy_prod
  - deploy_prod
  - postdeploy_prod
