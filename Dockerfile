FROM mcr.microsoft.com/dotnet/core/sdk:3.0.100-preview9 AS builder
#No need to install certificates here - no Internet requests made
WORKDIR /src
COPY src/WebRequests.csproj .
RUN dotnet restore
COPY src/ .
RUN dotnet publish -c Release -o /out WebRequests.csproj
FROM mcr.microsoft.com/dotnet/core/runtime:3.0.0-preview9
#Image runs internet requests over HTTPS - Install Certs if dev environment
#Set ARG BUILD_ENV default = production
ARG BUILD_ENV=production
#Assign the $BUILD_ENV the BUILD_ENV ENV so that it can be accessed
ENV BUILD_ENV $BUILD_ENV
#Add the CA Certificate to the container
ADD src/ZscalerRootCertificate-2048-SHA256.crt /tmp/ZscalerRootCertificate-2048-SHA256.crt
#Use BUILD_ENV variable within the container to copy the CA certificate into the certificate directory and update
RUN if [ "$BUILD_ENV" = "production" ] ; then echo "production env"; else echo
"non-production env: BUILD_ENV"; CERT_DIR=(openssl version -d | cut -f2 -d \")/certs ;
cp /tmp/ZscalerRootCertificate-2048-SHA256.crt $CERT_DIR ; update-ca-certificates ;
fi
#Continue the build where the HTTPS Connections are made
WORKDIR /app
ENTRYPOINT ["dotnet", "WebRequests.dll"]
ENV DotNetBot:Message="docker4theEdge!"
COPY --from=builder /out/ .
